#ifndef __PIC_H
#define __PIC_H 

//16λBMP 40X40 QQͼ��ȡģ����
//Image2LCDȡģѡ������
//ˮƽɨ��
//16λ
//40X40
//������ͼ��ͷ����
//��������
//�Զ�����
//��λ��ǰ
const unsigned char gImage_qq[3200] = { /* 0X00,0X10,0X28,0X00,0X28,0X00,0X01,0X1B, */
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XBE,0XF7,
0XFF,0XFF,0XDE,0XFF,0X38,0XC6,0X92,0X8C,0X8E,0X6B,0X6E,0X6B,0X10,0X7C,0X96,0XAD,
0X3C,0XE7,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0X5D,0XEF,
0X15,0X9D,0X4F,0X63,0X6C,0X42,0X0A,0X32,0X88,0X29,0X46,0X19,0X25,0X19,0X45,0X21,
0XE8,0X31,0X8E,0X6B,0X38,0XC6,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0X36,0XA5,0X10,0X53,
0X10,0X4B,0X51,0X53,0X0F,0X4B,0X6C,0X3A,0XE9,0X31,0X67,0X21,0X25,0X19,0XE4,0X10,
0XA3,0X08,0X62,0X00,0X83,0X08,0XCB,0X52,0X9A,0XD6,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0X3C,0XE7,0X70,0X63,0XB3,0X63,0XB8,0X7C,
0XF5,0X63,0X11,0X43,0X4D,0X32,0XEA,0X29,0X88,0X21,0X26,0X19,0X05,0X19,0X05,0X19,
0X04,0X11,0X04,0X11,0XE4,0X10,0X83,0X00,0XA3,0X08,0X72,0X8C,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XDB,0XDE,0X4B,0X3A,0XF0,0X42,0X35,0X6C,0X54,0X4B,
0XB1,0X32,0X2E,0X2A,0XEB,0X21,0XA9,0X21,0X67,0X19,0X05,0X19,0X04,0X11,0X04,0X11,
0X04,0X11,0X04,0X11,0X04,0X11,0X05,0X19,0XE4,0X10,0X42,0X00,0XAF,0X73,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0X5D,0XEF,0X09,0X32,0X4C,0X32,0X10,0X4B,0X8F,0X32,0X4F,0X2A,
0X2E,0X2A,0XCC,0X19,0X89,0X19,0X89,0X21,0X47,0X19,0X05,0X19,0X04,0X11,0X04,0X11,
0XC4,0X10,0XC4,0X10,0X04,0X11,0X04,0X11,0X04,0X11,0XE4,0X10,0X42,0X00,0X31,0X84,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XEC,0X52,0X47,0X19,0X4C,0X32,0X0B,0X2A,0XEC,0X21,0XEC,0X21,
0X0C,0X22,0X91,0X5B,0XEE,0X4A,0X06,0X11,0X26,0X19,0X04,0X19,0XE4,0X10,0XE4,0X10,
0XA7,0X29,0X66,0X21,0XA3,0X08,0X05,0X19,0X04,0X11,0X04,0X11,0XE4,0X10,0X82,0X00,
0XF7,0XBD,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0X35,0XA5,0X83,0X08,0X88,0X21,0X88,0X21,0X89,0X21,0XAA,0X21,0X8A,0X21,
0X6B,0X42,0X71,0X8C,0XFF,0XFF,0X72,0X8C,0X83,0X08,0X04,0X11,0XC4,0X08,0X29,0X42,
0XFB,0XDE,0X5D,0XEF,0XEC,0X5A,0X83,0X08,0X04,0X11,0X04,0X11,0X04,0X11,0X83,0X08,
0XE8,0X31,0XDF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XBE,0XF7,0XC7,0X31,0XC4,0X10,0X25,0X19,0X26,0X19,0X47,0X19,0X47,0X19,0XA8,0X29,
0X8A,0X52,0X28,0X4A,0X55,0XAD,0XFF,0XFF,0XE8,0X31,0XA3,0X08,0X05,0X19,0X4D,0X6B,
0X4D,0X6B,0XFF,0XFF,0X7D,0XEF,0X45,0X21,0XC4,0X10,0X04,0X11,0X04,0X11,0X04,0X11,
0X62,0X00,0X76,0XAD,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0X96,0XB5,0X62,0X00,0X04,0X11,0X04,0X19,0X05,0X11,0X05,0X19,0XC4,0X08,0X8B,0X4A,
0XB6,0XB5,0X5D,0XEF,0XF7,0XBD,0XFF,0XFF,0X8E,0X6B,0X62,0X00,0X29,0X42,0XAA,0X5A,
0X08,0X42,0XFF,0XFF,0XFF,0XFF,0XCC,0X52,0X83,0X08,0X04,0X11,0X04,0X11,0X04,0X11,
0XA3,0X08,0XAD,0X52,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0X1C,0XE7,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0X4E,0X63,0X62,0X00,0X04,0X11,0X04,0X11,0X04,0X11,0XE4,0X10,0X62,0X00,0X8E,0X63,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XCF,0X73,0X01,0X00,0XF3,0X9C,0X2C,0X63,
0X96,0XB5,0XFF,0XFF,0XFF,0XFF,0X2D,0X5B,0X83,0X00,0X04,0X11,0X04,0X11,0X04,0X11,
0XE4,0X10,0X67,0X21,0X3D,0XEF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XF8,0XBD,0XB7,0XB5,0X9E,0XEF,0XCB,0X52,0XB3,0X94,0XFF,0XFF,0XFF,0XFF,0XDF,0XFF,
0XE8,0X31,0XA3,0X08,0X04,0X11,0X04,0X11,0X04,0X11,0X04,0X11,0XA3,0X08,0X49,0X42,
0XFF,0XF7,0XFF,0XF7,0XFF,0XFF,0XFF,0XFF,0X6A,0X4A,0X01,0X00,0X72,0X84,0XFF,0XFF,
0XFF,0XFF,0XFF,0XF7,0XDF,0XEF,0X09,0X3A,0XA3,0X08,0X04,0X11,0X04,0X11,0X04,0X11,
0X04,0X11,0X05,0X11,0X18,0XBE,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XF0,0X7B,0X62,0X00,0XE8,0X31,0XC7,0X31,0X41,0X00,0X35,0XA5,0XFF,0XFF,0X5D,0XEF,
0X46,0X21,0XC4,0X10,0X04,0X11,0X04,0X11,0X04,0X11,0X04,0X11,0XE4,0X10,0XA3,0X08,
0X76,0X9D,0XFF,0XF7,0XFF,0XFF,0XB7,0XAD,0XA3,0X08,0XA3,0X08,0XC7,0X31,0X9E,0XE7,
0XFF,0XF7,0XFF,0XF7,0X76,0XA5,0XA3,0X08,0XE4,0X10,0X04,0X11,0X04,0X11,0X04,0X11,
0X05,0X11,0X05,0X11,0X35,0XA5,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XDB,0XDE,0XA7,0X29,0X83,0X00,0XC4,0X10,0XC4,0X10,0XC4,0X10,0X1C,0XE7,0X9E,0XEF,
0X05,0X11,0XE4,0X10,0X04,0X11,0X04,0X11,0X04,0X11,0X04,0X11,0X04,0X19,0XC4,0X08,
0XE5,0X10,0XD1,0X6B,0XD1,0X6B,0XC5,0X08,0X64,0X00,0XA5,0X08,0X43,0X00,0X2B,0X32,
0X77,0X9D,0XB3,0X84,0X25,0X19,0XC4,0X10,0X04,0X11,0X04,0X11,0X04,0X11,0X04,0X11,
0X25,0X19,0X26,0X09,0X35,0X9D,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XAF,0X73,0X62,0X00,0X04,0X19,0X05,0X19,0X82,0X00,0X0D,0X5B,0X8E,0X9B,
0X62,0X10,0X05,0X11,0X04,0X11,0X04,0X11,0X04,0X19,0XE4,0X10,0X85,0X00,0X05,0X11,
0XC4,0X39,0X81,0X5A,0X40,0X7B,0X22,0X9C,0X43,0XAC,0X03,0XA4,0X83,0X9B,0X82,0X72,
0X82,0X49,0XC2,0X18,0XA4,0X00,0XC5,0X00,0XE4,0X10,0X04,0X19,0X04,0X11,0X05,0X19,
0X47,0X19,0X67,0X11,0XEC,0X5A,0XBE,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XDF,0XFF,0XDB,0XDE,0XC4,0X10,0XE4,0X10,0X04,0X11,0X05,0X11,0XA4,0X18,0X01,0XC0,
0X83,0X88,0XE4,0X00,0X05,0X19,0X04,0X19,0XC5,0X08,0X44,0X21,0X43,0X83,0X23,0XD5,
0X42,0XFE,0XE4,0XFE,0X27,0XFF,0X07,0XFF,0XA4,0XFE,0X64,0XFE,0X03,0XFE,0XA3,0XFD,
0XE2,0XFC,0X42,0XEC,0X83,0XB3,0X24,0X62,0XE5,0X10,0XC4,0X08,0X04,0X19,0X26,0X19,
0XA8,0X19,0X87,0X21,0X00,0X90,0XD3,0XBC,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XDF,0XFF,0XFF,0XFF,0X10,0X7C,0X42,0X00,0X05,0X19,0X05,0X11,0X83,0X28,0X01,0XD0,
0X44,0XF8,0XA3,0X48,0XE4,0X00,0XC5,0X08,0X44,0X5A,0X02,0XED,0XE2,0XFD,0X02,0XFE,
0X66,0XFE,0X74,0XFF,0XB8,0XFF,0X73,0XFF,0XE7,0XF6,0XA6,0XF6,0X45,0XF6,0XA4,0XF5,
0XC3,0XFC,0X62,0XFC,0XC2,0XFC,0XC2,0XFC,0XE3,0XCB,0XC4,0X49,0X06,0X11,0X88,0X19,
0X87,0X01,0XA4,0X90,0X01,0XF8,0XEC,0X9A,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XBE,0XF7,0XE8,0X31,0X83,0X00,0X05,0X09,0X82,0X40,0X01,0XC0,
0X23,0XF8,0X85,0XF0,0XA3,0X48,0XA4,0X00,0X44,0X5A,0X02,0XFD,0X23,0XCC,0XC2,0XDC,
0X04,0XFE,0X28,0XFE,0X48,0XF6,0X46,0XF6,0X24,0XF6,0XE4,0XF5,0X64,0XFD,0XE3,0XFC,
0X62,0XFC,0XC2,0XFC,0X02,0XE4,0X02,0XDC,0XE2,0XFC,0XA4,0X7A,0X48,0X01,0X67,0X01,
0XC4,0X78,0X24,0XF8,0X02,0XF8,0X84,0XB0,0X7D,0XE7,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XDB,0XDE,0X25,0X19,0XA3,0X00,0XC4,0X38,0X02,0XE0,
0X22,0XD8,0X44,0XF8,0XA6,0XF8,0XA4,0X78,0X63,0X00,0X43,0X21,0X83,0X72,0X83,0X39,
0X82,0X9B,0X21,0XF5,0X61,0XFD,0X22,0XFD,0XE2,0XFC,0XA2,0XFC,0X42,0XFC,0X42,0XFC,
0X42,0XFC,0X22,0XAB,0X83,0X41,0XC3,0X92,0X04,0X52,0X26,0X01,0X25,0X19,0XA4,0X98,
0X44,0XF8,0X23,0XF8,0X02,0XF8,0XA4,0XD0,0X9E,0XEF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0X9A,0XD6,0X87,0X29,0XA5,0X00,0X43,0XB8,
0X22,0XF8,0X23,0XE0,0X65,0XF8,0XE8,0XF8,0X07,0XC9,0X83,0X48,0X42,0X00,0XA3,0X00,
0X84,0X00,0X63,0X29,0XA2,0X7A,0X62,0XB3,0XA2,0XCB,0X62,0XD3,0X02,0XBB,0X82,0X8A,
0X83,0X39,0XA4,0X00,0XE5,0X00,0XE5,0X00,0XE5,0X08,0XC4,0X60,0X64,0XD8,0X44,0XF8,
0X24,0XF8,0X23,0XF8,0X02,0XF8,0X83,0X88,0XDB,0XC6,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0X3D,0XE7,0X50,0X5B,0X08,0X31,
0X23,0XE8,0X43,0XF8,0X44,0XF0,0X65,0XF8,0X09,0XF9,0XAB,0XF9,0X89,0XD1,0X06,0X89,
0XA3,0X48,0X42,0X18,0X02,0X00,0X42,0X00,0X61,0X00,0X82,0X00,0X62,0X00,0X62,0X00,
0X83,0X00,0XA3,0X20,0XC4,0X50,0XA5,0X88,0X85,0XD8,0X65,0XF8,0X44,0XF8,0X44,0XF8,
0X23,0XF8,0X23,0XF8,0X03,0XD0,0X82,0X10,0XC7,0X29,0X5D,0XEF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0X6C,0X32,
0XA5,0X38,0X02,0XD8,0X23,0XF8,0X65,0XF8,0X66,0XF8,0XA7,0XF8,0X4A,0XF9,0X0C,0XFA,
0X4D,0XFA,0X4C,0XEA,0X0B,0XD2,0XA9,0XB9,0X68,0XB1,0X47,0XA9,0X27,0XB1,0X07,0XB9,
0X07,0XD1,0XE7,0XE8,0XC7,0XF8,0XA7,0XF8,0X65,0XF8,0X65,0XF8,0X44,0XF8,0X23,0XF8,
0X03,0XF8,0X02,0XD0,0XA3,0X28,0X05,0X09,0XC4,0X08,0XEC,0X5A,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFB,0XDE,0X05,0X19,
0XC4,0X00,0XA7,0X41,0XE6,0XC0,0X03,0XF8,0X86,0XF8,0XA7,0XF8,0X87,0XF8,0X86,0XF8,
0XC7,0XF8,0X29,0XF9,0X8A,0XF9,0XAB,0XF9,0XAB,0XF9,0X8B,0XF9,0X6A,0XF9,0X29,0XF9,
0X08,0XF9,0XC7,0XF8,0XA6,0XF8,0X86,0XF8,0X65,0XF8,0X64,0XF8,0X23,0XF8,0X02,0XF0,
0X06,0XB1,0X25,0X29,0XE4,0X00,0XE4,0X10,0X25,0X19,0X25,0X19,0X14,0X9D,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0X96,0XAD,0X62,0X00,
0X82,0X08,0X35,0X95,0XBA,0XCE,0X8B,0XA2,0X44,0XD0,0X25,0XF8,0X87,0XF8,0XA7,0XF8,
0XC7,0XF8,0XA7,0XF8,0X87,0XF8,0X86,0XF8,0X86,0XF8,0X86,0XF8,0X87,0XF8,0XA7,0XF8,
0XA7,0XF8,0XA6,0XF8,0X85,0XF8,0X65,0XF8,0X64,0XF8,0X24,0XF0,0X64,0XB8,0X0D,0X93,
0XBB,0XB6,0XCF,0X63,0X83,0X08,0X04,0X11,0XE4,0X10,0X66,0X21,0X49,0X3A,0X5D,0XEF,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XD3,0X94,0X42,0X00,
0XE4,0X10,0XBB,0XCE,0XFF,0XFF,0XBE,0XE7,0X76,0XB5,0XCC,0XAA,0X07,0XC1,0X45,0XE0,
0X45,0XF8,0X46,0XF8,0X66,0XF8,0X86,0XF8,0X86,0XF8,0X86,0XF8,0X86,0XF8,0X65,0XF8,
0X45,0XF8,0X65,0XF8,0X65,0XE8,0X44,0XD0,0X43,0XA8,0X01,0X88,0X82,0X90,0X3C,0XD7,
0XFF,0XEF,0X55,0X95,0X83,0X08,0X04,0X11,0X04,0X11,0X05,0X19,0X46,0X19,0XB3,0X94,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XB3,0X94,0X41,0X00,
0X86,0X21,0X5D,0XDF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XDF,0XE7,0X7A,0XC6,0XD3,0XB4,
0X4E,0XB3,0X2A,0XC2,0X68,0XD1,0XE6,0XE0,0XA6,0XE8,0XA5,0XE8,0XA5,0XE8,0XE6,0XD8,
0X88,0XC9,0X06,0XA9,0X22,0XA8,0X02,0XA8,0X00,0XA0,0X00,0XC8,0X00,0XD8,0XF7,0XE5,
0XFF,0XE7,0XF8,0XAD,0XC4,0X10,0XE4,0X10,0X04,0X11,0XE4,0X10,0X05,0X11,0X8B,0X4A,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0X55,0XA5,0X41,0X00,
0XA7,0X29,0X5D,0XDF,0XFF,0XF7,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XEF,0X7D,0XDF,0XDB,0XCE,0X59,0XCE,0XF8,0XCD,0XD7,0XCD,0XF7,0XC5,0X79,0XCE,
0XFB,0XBE,0XAB,0XA2,0X03,0XF0,0X45,0XF8,0X42,0XD0,0X43,0XE8,0X00,0XF0,0X72,0XD4,
0XFF,0XDF,0X39,0XAE,0XE4,0X10,0XE4,0X10,0X04,0X11,0XE4,0X10,0X05,0X11,0X87,0X29,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0X59,0XCE,0X83,0X08,
0X46,0X21,0X1C,0XD7,0XFF,0XF7,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XEF,0X8B,0XBA,0X04,0XF8,0X45,0XF8,0X62,0XE0,0X44,0XF0,0X00,0XF8,0X8E,0XDB,
0XFF,0XDF,0XF8,0XA5,0XC4,0X10,0XE4,0X10,0XE4,0X10,0X04,0X11,0XE4,0X10,0X25,0X19,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XBE,0XF7,0X87,0X29,
0X83,0X08,0X39,0XB6,0XFF,0XF7,0XDF,0XF7,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XBE,0XE7,0X4A,0XBA,0X03,0XF8,0X45,0XF8,0X64,0XF8,0X44,0XF8,0X00,0XF8,0X6E,0XE3,
0XFF,0XD7,0XF4,0X8C,0X83,0X08,0X04,0X11,0XE4,0X10,0XE4,0X10,0XE4,0X10,0X05,0X19,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XEF,0X73,
0X00,0X00,0X72,0X84,0XFF,0XEF,0XBE,0XEF,0XDF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XDF,0XE7,0X8B,0XBA,0X03,0XF8,0X45,0XF8,0X45,0XF8,0X23,0XF8,0X00,0XF8,0XD3,0XD4,
0XFF,0XD7,0X4E,0X5B,0X21,0X00,0X29,0X3A,0X55,0XA5,0X83,0X08,0XC4,0X10,0X25,0X19,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFB,0XDE,
0XA3,0X08,0XE8,0X31,0X9E,0XDF,0X9E,0XE7,0XBF,0XEF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XF7,0X51,0XBC,0X02,0XE0,0X03,0XF8,0X03,0XF0,0X43,0XE0,0XEC,0XC2,0X7E,0XCF,
0XFC,0XBE,0X46,0X21,0X21,0X00,0XD3,0X94,0XFF,0XFF,0X51,0X84,0X00,0X00,0X87,0X29,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0X51,0X84,0X00,0X00,0XF4,0X8C,0XFF,0XEF,0X9E,0XE7,0XBF,0XEF,0XDF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0X3D,0XDF,0X55,0XBD,0X52,0XBC,0X72,0XBC,0XB7,0XB5,0X5D,0XC7,0XFF,0XDF,
0XF0,0X6B,0X00,0X00,0X09,0X3A,0XBF,0XF7,0XFF,0XFF,0XFF,0XFF,0X14,0X9D,0X55,0XA5,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XAC,0X4A,0XA4,0X08,0XBB,0XBE,0XDF,0XE7,0X7E,0XE7,0XBE,0XEF,0XDF,0XF7,
0XDF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XDF,0XFF,0XDF,0XF7,0XFF,0XEF,0XDF,0XDF,0XBF,0XD7,0X9E,0XD7,0XDF,0XDF,0XD8,0XA5,
0X83,0X08,0X26,0X11,0XDB,0XD6,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0X79,0XEE,0X8B,0XDC,0X21,0X31,0XA9,0X21,0X3D,0XCF,0XBF,0XDF,0X7E,0XDF,0X9E,0XE7,
0XBE,0XEF,0XBF,0XEF,0XDF,0XF7,0XDF,0XF7,0XDF,0XF7,0XDF,0XF7,0XDF,0XF7,0XBF,0XEF,
0XBE,0XEF,0X9E,0XE7,0X7E,0XDF,0X5E,0XD7,0X5E,0XD7,0XDF,0XDF,0X9A,0XB6,0X26,0X19,
0X42,0X08,0XED,0XA3,0XBF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0X74,0XDD,
0XC0,0XDB,0X00,0XFE,0X42,0XEE,0X02,0X42,0X89,0X21,0X7B,0XB6,0XDF,0XDF,0X7E,0XD7,
0X7E,0XDF,0X7E,0XDF,0X9E,0XE7,0X9E,0XE7,0X9E,0XE7,0X9E,0XE7,0X9E,0XE7,0X7E,0XDF,
0X7E,0XDF,0X5D,0XD7,0X5D,0XD7,0X9E,0XDF,0XFF,0XE7,0XF8,0XA5,0X07,0X11,0XE3,0X18,
0X02,0XC5,0X60,0XFD,0XE6,0XD3,0XDB,0XEE,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0X9E,0XF7,0X84,0XBA,
0XC1,0XFC,0X42,0XFE,0X82,0XFE,0XA2,0XFE,0X81,0X83,0X45,0X21,0X74,0X74,0X5E,0XC7,
0XDF,0XDF,0X7E,0XD7,0X5E,0XD7,0X5D,0XD7,0X5E,0XD7,0X5E,0XD7,0X5D,0XD7,0X5D,0XD7,
0X5E,0XD7,0X9E,0XDF,0XFF,0XE7,0X3D,0XC7,0XF1,0X63,0X84,0X08,0X42,0X52,0X26,0XE6,
0X29,0XFF,0X86,0XFE,0XE0,0XF3,0X6A,0XC3,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0X18,0XDE,0XC1,0XD2,
0XA2,0XFD,0X22,0XFE,0X42,0XFE,0X42,0XFE,0X62,0XFE,0XE2,0XD4,0X41,0X6A,0X49,0X42,
0X53,0X74,0X3B,0XA6,0X3E,0XC7,0XBF,0XD7,0XBF,0XD7,0XBF,0XD7,0XBF,0XDF,0XBF,0XD7,
0X3E,0XC7,0X1A,0XA6,0XF2,0X63,0XA7,0X29,0X82,0X41,0X22,0XB4,0X62,0XFE,0X83,0XFE,
0XAA,0XFE,0X0F,0XFF,0X67,0XFD,0X63,0XBA,0X3C,0XEF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0X9A,0XE6,0X80,0XD2,
0X21,0XFD,0XC2,0XFD,0XE2,0XF5,0XC2,0XF5,0X82,0XF5,0X82,0XFD,0X62,0XFD,0X61,0XDC,
0X21,0X9B,0X84,0X6A,0XE9,0X6A,0X2C,0X63,0XAF,0X63,0X11,0X74,0X6E,0X63,0X2C,0X63,
0X89,0X5A,0X04,0X52,0X81,0X7A,0XC2,0XCB,0XE2,0XFC,0X62,0XFD,0X82,0XFD,0XC2,0XFD,
0XC2,0XFD,0XE4,0XFD,0X24,0XFD,0X62,0XCA,0X1C,0XE7,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XB1,0XCC,
0X81,0XD2,0XC0,0XF3,0XC1,0XFC,0X02,0XFD,0X02,0XFD,0XE2,0XFC,0XC2,0XFC,0XC2,0XFC,
0X81,0XFC,0X80,0XFB,0XC0,0XC9,0XA4,0X81,0X35,0XAD,0X59,0XCE,0X71,0X9C,0X21,0X81,
0X00,0XDA,0XA1,0XFB,0X82,0XFC,0XA2,0XFC,0X82,0XFC,0XA2,0XFC,0X02,0XFD,0X22,0XFD,
0XE2,0XFC,0X00,0XFC,0X60,0XDA,0X90,0XCC,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
0X59,0XDE,0X0D,0XC4,0X06,0XCB,0XE4,0XD2,0X03,0XDB,0X03,0XDB,0XE3,0XDA,0XC3,0XD2,
0XA4,0XC2,0X09,0XB3,0XD2,0XBC,0X9E,0XF7,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFB,0XE6,
0X0E,0XB4,0XA6,0XBA,0X83,0XD2,0XE3,0XE2,0X02,0XEB,0X22,0XEB,0X22,0XE3,0X03,0XDB,
0XE4,0XD2,0X6A,0XC3,0XB6,0XD5,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,0XFF,
};
const unsigned char gImage_12[7200] = { /* 0X00,0X10,0X3C,0X00,0X3C,0X00,0X01,0X1B, */
0X1E,0XEF,0X1C,0XD6,0X3C,0XD6,0X1E,0XEF,0X3E,0XF7,0X3E,0XF7,0X3F,0XF7,0X7A,0XBD,
0X3C,0XD6,0X3C,0XD6,0XFC,0XCD,0XFB,0XCD,0X1C,0XD6,0X3C,0XD6,0X3C,0XD6,0X1B,0XCE,
0X5C,0XDE,0X3F,0XF7,0X5F,0XF7,0X3E,0XF7,0XDD,0XEE,0X3E,0XF7,0X3E,0XF7,0X5F,0XF7,
0X5E,0XF7,0X5F,0XF7,0X5E,0XF7,0X5F,0XF7,0X5E,0XF7,0X3E,0XF7,0X3E,0XEF,0XDD,0XE6,
0X1F,0XF7,0X3E,0XF7,0X3E,0XF7,0X3E,0XEF,0X3E,0XEF,0X3E,0XF7,0X1E,0XF7,0X1E,0XEF,
0X1E,0XEF,0X1E,0XF7,0X1E,0XEF,0X1E,0XEF,0X1E,0XEF,0X1E,0XEF,0X1E,0XEF,0XFE,0XEE,
0X1D,0XEF,0XFE,0XEE,0XDD,0XE6,0XF7,0XB4,0XBB,0XCD,0XDA,0XCD,0X7C,0XDE,0XBA,0XCD,
0XBD,0XE6,0X7C,0XE6,0X5C,0XE6,0X7C,0XE6,0XDB,0XCD,0XFC,0XCD,0X3C,0XD6,0XFE,0XEE,
0XDE,0XEE,0X3E,0XF7,0X3F,0XF7,0XBB,0XCD,0X3B,0XD6,0X3B,0XD6,0XBB,0XC5,0X1C,0XD6,
0XBA,0XC5,0X9D,0XE6,0X1E,0XF7,0X3E,0XF7,0X3E,0XF7,0X3E,0XF7,0X3F,0XF7,0X3E,0XF7,
0X3E,0XF7,0XBC,0XE6,0X3E,0XF7,0X3E,0XF7,0X5E,0XF7,0X5E,0XF7,0X3E,0XF7,0X3F,0XF7,
0X5F,0XF7,0X3E,0XF7,0XDD,0XEE,0X9A,0XC5,0X1E,0XF7,0X1E,0XF7,0X3E,0XEF,0X7C,0XDE,
0X1E,0XEF,0X1E,0XEF,0X1E,0XF7,0X1E,0XEF,0XDA,0XD5,0XFE,0XEE,0X1E,0XEF,0X1E,0XEF,
0X1E,0XEF,0X1E,0XEF,0X1D,0XEF,0XFE,0XEE,0XFD,0XEE,0XFD,0XEE,0XFE,0XEE,0XDD,0XE6,
0XBE,0XE6,0XBD,0XE6,0XDD,0XE6,0XBD,0XE6,0XBD,0XE6,0X9A,0XC5,0X38,0XBD,0X99,0XC5,
0XDB,0XCD,0X1C,0XCE,0X3C,0XD6,0X7C,0XDE,0X7C,0XDE,0X3F,0XF7,0X1E,0XF7,0X1B,0XD6,
0XFB,0XCD,0XDB,0XCD,0X1F,0XEF,0X1E,0XF7,0X3F,0XF7,0X3F,0XF7,0X3F,0XF7,0X5F,0XF7,
0X5F,0XF7,0X5F,0XF7,0X5F,0XF7,0X5E,0XF7,0X3E,0XF7,0X1E,0XEF,0X3E,0XF7,0X3F,0XF7,
0X5E,0XF7,0X3E,0XF7,0X3E,0XF7,0X1E,0XF7,0X3E,0XF7,0XFE,0XEE,0X3C,0XD6,0XDA,0XCD,
0X1E,0XEF,0X1E,0XF7,0X3B,0XD6,0XFE,0XEE,0XFA,0XCD,0XDD,0XEE,0X1E,0XEF,0X7C,0XE6,
0XBA,0XDD,0XDD,0XEE,0XDD,0XEE,0XDE,0XEE,0X3B,0XDE,0X38,0XC5,0XDD,0XEE,0XFE,0XEE,
0XFE,0XEE,0XFD,0XEE,0XFD,0XEE,0XDD,0XEE,0XDA,0XCD,0XBD,0XE6,0XDD,0XE6,0X9D,0XE6,
0X9A,0XC5,0X39,0XBD,0XFB,0XD5,0X7C,0XE6,0X19,0XB5,0X1C,0XD6,0X3C,0XD6,0X3C,0XD6,
0XFE,0XF6,0X1E,0XEF,0X5B,0XD6,0X1F,0XF7,0X1E,0XEF,0X5F,0XF7,0X5F,0XF7,0X5F,0XF7,
0X5F,0XF7,0X5F,0XF7,0X1E,0XEF,0X3E,0XF7,0X3F,0XF7,0X5F,0XF7,0X5F,0XF7,0X5E,0XF7,
0X3E,0XF7,0X1E,0XEF,0XFB,0XCD,0XFE,0XEE,0X9C,0XDE,0X5C,0XDE,0X3C,0XD6,0X3C,0XD6,
0X7C,0XDE,0X3C,0XD6,0X1C,0XCE,0XFE,0XEE,0X3E,0XEF,0X1E,0XEF,0X1E,0XEF,0X58,0XB5,
0XBA,0XC5,0X1E,0XEF,0XFE,0XEE,0X58,0XC5,0X99,0XDD,0X17,0XC5,0X58,0XD5,0X54,0XB4,
0XB6,0XC4,0X18,0XCD,0XB9,0XD5,0XDD,0XEE,0X1E,0XEF,0XFD,0XEE,0XFD,0XEE,0XDD,0XE6,
0XDD,0XE6,0X5C,0XDE,0X9D,0XE6,0XBA,0XC5,0X56,0XA4,0X5A,0XC5,0X5D,0XDE,0X9C,0XE6,
0XFC,0XCD,0X1C,0XD6,0X3C,0XD6,0X99,0XC5,0X3F,0XF7,0X3E,0XF7,0X5E,0XF7,0X5F,0XF7,
0X5E,0XF7,0X3F,0XF7,0X5F,0XF7,0X5F,0XF7,0X3F,0XF7,0X5F,0XF7,0X5F,0XF7,0X5D,0XDE,
0XDA,0XCD,0X3E,0XF7,0X3E,0XF7,0X3F,0XF7,0X9D,0XE6,0X3C,0XD6,0XDC,0XD5,0XDB,0XD5,
0XFB,0XD5,0XBB,0XD5,0X7A,0XCD,0X59,0XC5,0X7A,0XCD,0XDB,0XD5,0XFB,0XD5,0XFE,0XEE,
0X1E,0XEF,0X7C,0XDE,0X7D,0XE6,0XFC,0XCD,0X9E,0XE6,0X1E,0XEF,0X1E,0XEF,0X79,0XCD,
0X79,0XDD,0XF7,0XC4,0XB2,0XAB,0X37,0XD5,0X38,0XDD,0X1B,0XE6,0XFE,0XEE,0XFE,0XEE,
0XFE,0XEE,0XFD,0XEE,0XFD,0XEE,0XFE,0XEE,0XDD,0XE6,0X5C,0XDE,0XBA,0XCD,0X56,0XA4,
0X7A,0XC5,0X5A,0XC5,0XB7,0XB4,0XFB,0XD5,0XFB,0XCD,0X9D,0XE6,0X3E,0XF7,0X3E,0XF7,
0X3F,0XF7,0X5F,0XF7,0X5E,0XF7,0X5F,0XF7,0X5F,0XF7,0X3F,0XF7,0X5F,0XF7,0X5F,0XF7,
0X5F,0XF7,0X5F,0XF7,0X5E,0XF7,0X5F,0XF7,0X5C,0XDE,0XDA,0XCD,0X9A,0XC5,0X1C,0XCE,
0X1C,0XD6,0XDB,0XD5,0X14,0XA4,0X17,0XCD,0X37,0XD5,0X37,0XCD,0X57,0XD5,0X78,0XD5,
0XD9,0XE5,0XD9,0XDD,0XFA,0XDD,0XD9,0XD5,0X5B,0XE6,0XFE,0XF6,0XDA,0XCD,0XDC,0XCD,
0X1D,0XEF,0X3D,0XF7,0X3D,0XF7,0XDD,0XF6,0X99,0XD5,0X96,0XBC,0X38,0XDD,0X38,0XD5,
0X38,0XD5,0X78,0XD5,0XBD,0XEE,0XDE,0XEE,0XFD,0XEE,0XFD,0XEE,0XFD,0XEE,0XDD,0XE6,
0XFD,0XEE,0XBD,0XE6,0X5A,0XC5,0X5A,0XC5,0X5A,0XC5,0X5A,0XC5,0X39,0XBD,0XF8,0XBC,
0X1F,0XF7,0X3F,0XF7,0X5E,0XF7,0X5F,0XF7,0X5F,0XF7,0X5F,0XF7,0X5F,0XF7,0X5F,0XF7,
0X7E,0XF7,0X5E,0XF7,0X3F,0XF7,0X5E,0XF7,0X5F,0XF7,0X5F,0XF7,0X5F,0XF7,0X3E,0XF7,
0X3E,0XF7,0X3C,0XD6,0XBB,0XCD,0X14,0X9C,0XD6,0XBC,0X16,0XCD,0X37,0XD5,0X77,0XD5,
0XF9,0XE5,0XF9,0XDD,0X1A,0XE6,0X1A,0XE6,0X1A,0XE6,0XFA,0XDD,0XDA,0XE5,0XFA,0XE5,
0XFA,0XE5,0X99,0XD5,0XDB,0XD5,0XB6,0XAC,0X1E,0XF7,0X1D,0XEF,0X1E,0XEF,0XBE,0XEE,
0XF7,0XBC,0X5B,0XE6,0X58,0XD5,0X79,0XD5,0X79,0XD5,0X79,0XD5,0X3B,0XE6,0XBD,0XE6,
0XFE,0XEE,0XDE,0XEE,0XDD,0XEE,0XFD,0XEE,0XDD,0XE6,0XDD,0XEE,0X59,0XC5,0X7A,0XC5,
0X76,0XA4,0X5A,0XC5,0X3A,0XBD,0X19,0XBD,0X5E,0XF7,0X5F,0XF7,0X3F,0XF7,0X5F,0XF7,
0X5F,0XF7,0X5F,0XF7,0X5F,0XF7,0X5E,0XF7,0X5F,0XF7,0X5F,0XF7,0XFE,0XEE,0X3E,0XF7,
0X5F,0XF7,0X5E,0XF7,0X5F,0XF7,0X3E,0XF7,0X1E,0XEF,0X3C,0XD6,0XD7,0XBC,0X16,0XCD,
0X57,0XD5,0XD9,0XDD,0X1A,0XE6,0X19,0XE6,0X1A,0XE6,0X98,0XDD,0X37,0XD5,0X36,0XD5,
0X16,0XCD,0XD5,0XC4,0X16,0XCD,0X16,0XCD,0X16,0XCD,0X16,0XCD,0XF6,0XCC,0X58,0XC5,
0X5C,0XDE,0XFE,0XEE,0XFE,0XEE,0XFE,0XEE,0XF4,0X9B,0X7C,0XE6,0X5C,0XE6,0X5C,0XE6,
0X55,0XB4,0XF4,0XAB,0X76,0XB4,0XB3,0X9B,0X5D,0XE6,0XDE,0XEE,0XBD,0XEE,0X9D,0XE6,
0XBD,0XE6,0XBD,0XE6,0XBD,0XE6,0X5A,0XC5,0X7A,0XC5,0X7A,0XC5,0X79,0XC5,0XFB,0XD5,
0X5E,0XF7,0X5E,0XF7,0X5E,0XF7,0X5F,0XF7,0X5F,0XF7,0X5F,0XF7,0X5F,0XF7,0X5E,0XF7,
0X5F,0XF7,0X5E,0XF7,0XDD,0XE6,0X7B,0XDE,0XFE,0XF6,0X3E,0XF7,0X5E,0XF7,0X1E,0XEF,
0X3C,0XD6,0X75,0XA4,0X58,0XCD,0XD9,0XDD,0X1A,0XE6,0XF9,0XE5,0XB8,0XDD,0X57,0XD5,
0XD5,0XC4,0XF5,0XCC,0X16,0XCD,0XF6,0XCC,0X74,0XC4,0XD5,0XCC,0X73,0XBC,0X12,0XBC,
0XF6,0XCC,0XF6,0XCC,0XF6,0XCC,0XD6,0XC4,0X18,0XC5,0XFC,0XCD,0X9D,0XE6,0X1E,0XEF,
0XFB,0XD5,0X5C,0XE6,0X7C,0XE6,0X7C,0XE6,0XF8,0XC4,0XBB,0XE5,0X9B,0XDD,0X9C,0XDD,
0X9C,0XDD,0X36,0XAC,0X9A,0XCD,0XDA,0XCD,0XDD,0XEE,0XDD,0XE6,0XBC,0XE6,0X39,0XBD,
0X7D,0XE6,0X9D,0XE6,0X7D,0XE6,0XBA,0XCD,0X5E,0XF7,0X5E,0XF7,0X5F,0XF7,0X5F,0XF7,
0X5F,0XF7,0X5F,0XF7,0X5E,0XF7,0X3F,0XF7,0X5E,0XF7,0X1E,0XF7,0XBD,0XE6,0XBD,0XEE,
0XBA,0XDD,0XFE,0XF6,0X3F,0XF7,0X1C,0XD6,0X79,0XC5,0X98,0XD5,0X1A,0XE6,0X1A,0XE6,
0XF9,0XDD,0X33,0XBC,0X16,0XD5,0X16,0XD5,0XF5,0XCC,0XD1,0XB3,0XD5,0XCC,0XF5,0XC4,
0XD5,0XC4,0X94,0XC4,0XF6,0XCC,0X32,0XBC,0X90,0XA3,0XF5,0XCC,0XF6,0XCC,0XF5,0XCC,
0X94,0XBC,0X38,0XC5,0XFB,0XCD,0XFE,0XEE,0X3C,0XD6,0XDB,0XCD,0X7A,0XCD,0X1C,0XE6,
0X9C,0XE5,0X9C,0XE5,0X9B,0XDD,0X9C,0XDD,0X9B,0XDD,0X7B,0XDD,0X3E,0XEE,0XBD,0XE6,
0XDD,0XE6,0XDD,0XE6,0XBD,0XE6,0X9C,0XE6,0X7D,0XE6,0XFB,0XD5,0X7D,0XE6,0XDB,0XCD,
0X5F,0XF7,0X5F,0XF7,0X5F,0XF7,0X5F,0XF7,0X3F,0XF7,0X5F,0XF7,0X3E,0XF7,0X3E,0XF7,
0X3E,0XF7,0X9D,0XE6,0X38,0XC5,0X9D,0XEE,0X38,0XD5,0XF6,0XBC,0X3B,0XD6,0X1C,0XD6,
0XB9,0XD5,0X3A,0XDE,0X1A,0XE6,0X77,0XD5,0X16,0XCD,0X53,0XBC,0XD5,0XCC,0XF6,0XD4,
0X16,0XCD,0X13,0XBC,0X2E,0XA3,0X53,0XBC,0XD5,0XCC,0XB5,0XCC,0XB4,0XC4,0XD5,0XCC,
0X53,0XBC,0X90,0XA3,0XF5,0XCC,0XF5,0XCC,0XF5,0XCC,0XD2,0XAB,0XBA,0XCD,0X7E,0XE6,
0XFB,0XD5,0XDB,0XCD,0XBB,0XCD,0XF5,0XAB,0X9C,0XE5,0X9B,0XDD,0X9B,0XDD,0X9B,0XDD,
0X9C,0XDD,0X7B,0XDD,0X1A,0XCD,0X9D,0XE6,0XDD,0XEE,0XDD,0XE6,0XBD,0XE6,0XBD,0XE6,
0X7C,0XDE,0X7D,0XE6,0XBD,0XEE,0X59,0XC5,0X5F,0XF7,0X3E,0XF7,0X5F,0XF7,0X5E,0XF7,
0X5F,0XF7,0X5E,0XF7,0X3E,0XF7,0X1E,0XF7,0XDD,0XEE,0XD6,0XBC,0X99,0XD5,0XBA,0XDD,
0X95,0XBC,0XF3,0X9B,0X1B,0XD6,0XB6,0XB4,0X1A,0XE6,0X19,0XDE,0X57,0XD5,0X32,0XB4,
0XD6,0XCC,0XD5,0XCC,0XF1,0XB3,0XF6,0XD4,0XF5,0XCC,0XB4,0XC4,0X90,0XAB,0X6F,0XAB,
0XB1,0XB3,0XD5,0XCC,0X33,0XBC,0X94,0XC4,0XF5,0XCC,0X73,0XC4,0XB4,0XC4,0XF5,0XCC,
0XF5,0XC4,0X74,0XC4,0X91,0X9B,0XDC,0XCD,0XFC,0XD5,0XDB,0XCD,0XBC,0XCD,0X17,0XB4,
0X9B,0XDD,0X9C,0XDD,0X9B,0XDD,0X9B,0XDD,0X7B,0XDD,0X7B,0XDD,0X7B,0XDD,0X15,0XA4,
0XBD,0XE6,0XDC,0XE6,0X9D,0XE6,0XBD,0XE6,0XB9,0XC5,0X7C,0XE6,0X5C,0XDE,0X39,0XBD,
0X3E,0XF7,0X3B,0XD6,0X3E,0XF7,0X3E,0XF7,0X5E,0XF7,0X3E,0XF7,0X1E,0XEF,0XBA,0XD5,
0X75,0XB4,0XB2,0XAB,0X79,0XDD,0X99,0XDD,0X14,0XAC,0XFB,0XDD,0XB6,0XB4,0XDA,0XE5,
0XF9,0XDD,0X37,0XD5,0XF5,0XCC,0X6F,0XA3,0XF5,0XCC,0XB4,0XC4,0X4F,0XA3,0XD1,0XB3,
0XF6,0XD4,0XF5,0XCC,0X70,0XAB,0XAC,0X92,0XB0,0XB3,0XB1,0XB3,0X74,0XC4,0X70,0XA3,
0XD5,0XC4,0XD5,0XC4,0X11,0XB4,0XD5,0XCC,0XF5,0XCC,0XF5,0XC4,0XF1,0XAB,0X39,0XC5,
0XDC,0XD5,0XDB,0XD5,0X9B,0XCD,0XB6,0XAB,0XBA,0XD4,0X7B,0XDD,0X9B,0XDD,0X9B,0XDD,
0X7B,0XDD,0X7B,0XDD,0X7B,0XDD,0XD9,0XCC,0X3A,0XCD,0X17,0XB5,0X9D,0XE6,0XBC,0XE6,
0XBD,0XE6,0XF8,0XB4,0X59,0XC5,0X7A,0XC5,0X5E,0XF7,0X3E,0XF7,0X3B,0XD6,0X3E,0XF7,
0X5E,0XF7,0X3E,0XF7,0X3E,0XF7,0XDE,0XF6,0X99,0XD5,0XF6,0XCC,0XD6,0XCC,0X58,0XDD,
0X5C,0XE6,0X38,0XBD,0X14,0XB4,0XB9,0XDD,0X36,0XCD,0X16,0XD5,0XF5,0XCC,0XF1,0XB3,
0XD5,0XCC,0XF2,0XAB,0X77,0XD5,0X70,0XAB,0X90,0XAB,0XD5,0XCC,0X12,0XBC,0X4F,0XA3,
0X0E,0XA3,0X2F,0XA3,0X90,0XAB,0XD1,0XB3,0X33,0XBC,0XB1,0XAB,0X94,0XC4,0X93,0XC4,
0XF5,0XCC,0XF5,0XCC,0XD4,0XCC,0X92,0XA3,0XBB,0XCD,0XFC,0XCD,0XBB,0XCD,0X7B,0XCD,
0XF7,0XC3,0X1B,0XDD,0X3A,0XD5,0X7C,0XDD,0X7B,0XDD,0X7B,0XDD,0X5B,0XDD,0X3A,0XD5,
0XDA,0XCC,0X5D,0XE6,0XBD,0XE6,0XBD,0XE6,0XBD,0XE6,0X38,0XBD,0X9D,0XE6,0X7C,0XE6,
0X3E,0XF7,0X3E,0XF7,0X1E,0XF7,0XFA,0XCD,0X3F,0XF7,0X3E,0XF7,0X3E,0XEF,0X1F,0XFF,
0X99,0XD5,0X58,0XDD,0X95,0XB4,0X9C,0XEE,0X5C,0XE6,0X39,0XC5,0X91,0XAB,0X16,0XCD,
0X16,0XCD,0X16,0XCD,0XF5,0XCC,0X53,0XC4,0XF6,0XD4,0X99,0XDD,0XB9,0XDD,0X70,0XA3,
0X90,0XB3,0XB0,0XAB,0X74,0XC4,0X4E,0XA3,0XCD,0X92,0X2F,0XA3,0X70,0XAB,0X70,0XAB,
0X4F,0X9B,0XB4,0XCC,0XB4,0XC4,0X4F,0X9B,0XD4,0XC4,0XF5,0XCC,0XD5,0XCC,0XD2,0XB3,
0X79,0XCD,0XDB,0XCD,0XBB,0XCD,0XBB,0XCD,0X57,0XB4,0XD6,0XBB,0XD6,0XB3,0X5B,0XDD,
0X7B,0XDD,0X7B,0XDD,0X7B,0XD5,0X5B,0XDD,0XD6,0XAB,0XB8,0XC4,0X9D,0XE6,0XBD,0XE6,
0XBD,0XE6,0X9D,0XE6,0X7C,0XE6,0X5C,0XDE,0X3E,0XF7,0X5E,0XF7,0X5E,0XF7,0X1E,0XEF,
0X99,0XC5,0X3F,0XF7,0X1E,0XF7,0X79,0XD5,0X95,0XB4,0X9C,0XEE,0XBD,0XEE,0XBC,0XEE,
0XB6,0XAC,0X10,0X93,0X70,0XAB,0XF5,0XCC,0XF5,0XCC,0X15,0XCD,0XF5,0XCC,0XD4,0XCC,
0XF5,0XCC,0X5C,0XEE,0X9C,0XEE,0X98,0XDD,0XB1,0XB3,0X0E,0X9B,0X4F,0XA3,0X2E,0XA3,
0X4F,0XAB,0X37,0XD5,0X2F,0XA3,0X91,0XAB,0X90,0XAB,0XB1,0XAB,0X94,0XC4,0X2F,0XA3,
0XB4,0XC4,0XD5,0XC4,0XF5,0XCC,0XB5,0XC4,0XB2,0X9B,0X9B,0XCD,0XBB,0XCD,0XDB,0XC5,
0XBB,0XCD,0X94,0X9B,0XB6,0XB3,0X3B,0XDD,0X7B,0XDD,0X7B,0XDD,0X5B,0XDD,0X7B,0XDD,
0X1A,0XD5,0X3B,0XDD,0XD7,0XBC,0X9C,0XE6,0XBC,0XE6,0X9D,0XE6,0X59,0XC5,0X5A,0XC5,
0X3E,0XF7,0X3E,0XF7,0X3E,0XF7,0X5E,0XF7,0X5D,0XDE,0X5A,0XC5,0X75,0XB4,0X35,0XBC,
0XF8,0XCC,0X5C,0XEE,0XBD,0XEE,0X7C,0XE6,0XBB,0XD5,0X92,0XAB,0X70,0XAB,0XF5,0XCC,
0XF6,0XCC,0XF6,0XCC,0XF5,0XCC,0XD5,0XCC,0XF5,0XCC,0XF6,0XBC,0XBD,0XEE,0XBD,0XEE,
0XB9,0XDD,0XF2,0XB3,0X4F,0XA3,0X2F,0XA3,0X70,0XAB,0XBA,0XE5,0XDA,0XDD,0X71,0XA3,
0XB1,0XAB,0X2F,0X9B,0X90,0XA3,0X6F,0XA3,0XD1,0XB3,0XD4,0XC4,0XD5,0XC4,0X94,0XC4,
0X13,0XB4,0X38,0XC5,0XDB,0XCD,0XBB,0XCD,0XBB,0XCD,0X9B,0XC5,0X33,0X9B,0XDA,0XD4,
0X7B,0XDD,0X7B,0XDD,0X5B,0XDD,0X5B,0XDD,0X3B,0XD5,0XB5,0XAB,0X3A,0XD5,0X7D,0XE6,
0XBC,0XE6,0X9C,0XE6,0X56,0XA4,0X36,0XA4,0X3B,0XD6,0XFE,0XEE,0X3E,0XF7,0X1E,0XF7,
0X1F,0XF7,0XDC,0XE5,0XFC,0XE5,0XDC,0XE5,0XDD,0XED,0XFD,0XED,0XDB,0XDD,0X9A,0XCD,
0XBA,0XD5,0X71,0XAB,0X90,0XA3,0XF6,0XCC,0X16,0XD5,0XF5,0XCC,0XF5,0XCC,0XB4,0XCC,
0X91,0X9B,0X9D,0XEE,0XDC,0XEE,0XBC,0XEE,0XBD,0XEE,0X37,0XD5,0XB5,0XCC,0X50,0XAB,
0X6F,0XAB,0X71,0XA3,0XB9,0XDD,0X1B,0XE6,0XB6,0XC4,0X33,0XBC,0XFA,0XE5,0XED,0X8A,
0XB0,0XA3,0XD4,0XC4,0XF5,0XC4,0X94,0XC4,0X4E,0X9B,0XD2,0XA3,0X9B,0XCD,0XBB,0XC5,
0XBB,0XC5,0X9A,0XC5,0X7A,0XC5,0X94,0XAB,0X3B,0XDD,0X5B,0XDD,0X5B,0XDD,0X3B,0XDD,
0X5B,0XD5,0X1A,0XDD,0X1A,0XD5,0XB8,0XBC,0X7D,0XE6,0X9C,0XDE,0XDB,0XD5,0XF9,0XB4,
0X3E,0XF7,0XBB,0XC5,0X3C,0XD6,0XBD,0XE6,0X7E,0XEE,0XFC,0XE5,0XFD,0XE5,0XFD,0XE5,
0XFC,0XE5,0XFC,0XE5,0X9C,0XE5,0XD9,0XBC,0X9B,0XD5,0X2F,0X9B,0XD1,0XAB,0XF1,0XAB,
0XF6,0XCC,0XF6,0XCC,0X15,0XCD,0XD1,0XAB,0X53,0XB4,0XBD,0XEE,0XDC,0XEE,0XDD,0XEE,
0XDD,0XEE,0XDD,0XEE,0X95,0XB4,0X79,0XE5,0XB1,0XAB,0XD2,0XB3,0X1B,0XEE,0X54,0XB4,
0X94,0XB4,0XC9,0X59,0X4C,0X6A,0X1A,0XE6,0X94,0XC4,0XD4,0XC4,0XD4,0XC4,0XD4,0XC4,
0X90,0XA3,0XB1,0XAB,0XF7,0XBC,0XBB,0XC5,0XBB,0XCD,0XBB,0XC5,0X9B,0XC5,0X58,0XB4,
0XDA,0XD4,0X5B,0XDD,0X7B,0XDD,0X5A,0XDD,0X3B,0XD5,0X3B,0XDD,0X3A,0XDD,0X1A,0XD5,
0X3A,0XCD,0X9C,0XDE,0X5D,0XE6,0XD8,0XB4,0X3E,0XF7,0X7C,0XDE,0X79,0XBD,0X3C,0XD6,
0X5A,0XD5,0XDC,0XE5,0XFC,0XE5,0XFC,0XE5,0XFC,0XE5,0XFD,0XE5,0X1B,0XDD,0XD9,0XC4,
0X97,0XB4,0X0F,0X93,0XB0,0XAB,0XD1,0XAB,0XF5,0XCC,0XF5,0XCC,0XD5,0XCC,0X11,0XB4,
0X78,0XD5,0XDD,0XEE,0XDC,0XEE,0XBC,0XEE,0XD9,0XDD,0X9C,0XEE,0XBC,0XEE,0XD6,0XB4,
0X1B,0XE6,0X13,0XB4,0X1B,0XE6,0X7C,0XE6,0X7C,0XEE,0X5C,0XE6,0X7C,0XEE,0X5B,0XE6,
0X94,0XC4,0XB4,0XCC,0XB4,0XC4,0XD5,0XCC,0X53,0XBC,0XD1,0XB3,0X4F,0X93,0X9A,0XC5,
0XBB,0XC5,0X9B,0XC5,0X9A,0XC5,0X7A,0XC5,0XD6,0XB3,0X3B,0XD5,0X3A,0XD5,0X3A,0XD5,
0X3A,0XDD,0X3A,0XDD,0X3A,0XD5,0X1A,0XD5,0X1A,0XCD,0X5D,0XE6,0X9C,0XDE,0XB7,0XAC,
0X3E,0XF7,0X3E,0XF7,0X1E,0XEF,0X39,0XBD,0XDC,0XE5,0XFC,0XE5,0XFC,0XE5,0X1C,0XE6,
0XFC,0XE5,0XFD,0XE5,0X79,0XD4,0XBC,0XDD,0XDB,0XCD,0XCF,0X8A,0X90,0XAB,0X32,0XB4,
0XF6,0XCC,0XF5,0XCC,0XF6,0XCC,0XB1,0XAB,0X3C,0XEE,0XBC,0XEE,0XDD,0XEE,0X9C,0XE6,
0XDA,0XD5,0XBD,0XEE,0XBD,0XEE,0XBC,0XEE,0XBC,0XEE,0X1C,0XE6,0X98,0XCD,0XBC,0XEE,
0XBC,0XEE,0XBC,0XEE,0X9C,0XEE,0X5B,0XE6,0X94,0XC4,0X93,0XC4,0XD4,0XC4,0XB4,0XCC,
0X94,0XC4,0X70,0XA3,0X6F,0XA3,0XD2,0XAB,0X9A,0XCD,0X9B,0XC5,0XBB,0XC5,0X7A,0XC5,
0X75,0X9B,0XD9,0XD4,0X55,0XAB,0X1B,0XDD,0X1B,0XDD,0X1B,0XDD,0X1A,0XDD,0X1A,0XD5,
0XFA,0XD4,0XF6,0XAB,0X5C,0XDE,0X79,0XC5,0X3E,0XF7,0X3E,0XF7,0X1E,0XF7,0XDF,0XF6,
0XFD,0XE5,0XDD,0XED,0XFD,0XE5,0XFD,0XE5,0XFC,0XE5,0XDC,0XE5,0X19,0XC4,0XBC,0XD5,
0XFC,0XCD,0X30,0X93,0X70,0XAB,0X94,0XC4,0XF5,0XCC,0X16,0XCD,0X16,0XCD,0XB4,0XC4,
0X7C,0XEE,0XBD,0XEE,0XBC,0XEE,0XFA,0XD5,0X2C,0X62,0X9D,0XEE,0XDD,0XEE,0XDC,0XEE,
0XDD,0XEE,0XBC,0XEE,0X7C,0XE6,0XBC,0XEE,0XBC,0XEE,0XBC,0XEE,0X9C,0XEE,0X5B,0XE6,
0X94,0XC4,0XF1,0XB3,0XB4,0XC4,0XB4,0XC4,0X74,0XC4,0X2E,0X9B,0X33,0XBC,0X0D,0X93,
0X71,0X93,0X9A,0XC5,0X9B,0XC5,0X9A,0XC5,0X5B,0XCD,0X13,0XA3,0X79,0XD4,0X17,0XBC,
0XFA,0XD4,0X3A,0XDD,0X1A,0XD5,0X1A,0XD5,0XFA,0XD4,0X1B,0XD5,0X5C,0XDE,0X5C,0XDE,
0X5C,0XD6,0XFB,0XCD,0X59,0XBD,0X7A,0XCD,0XDC,0XE5,0XF9,0XCC,0XDC,0XE5,0XFD,0XE5,
0XFC,0XE5,0XDD,0XED,0XF8,0XBB,0XFC,0XD5,0XFC,0XD5,0X10,0X93,0X6F,0XA3,0XB4,0XCC,
0XF5,0XCC,0XF5,0XCC,0XF5,0XCC,0XF5,0XC4,0X3C,0XEE,0X50,0X8B,0XEB,0X59,0X6C,0X6A,
0XBD,0XEE,0XDD,0XF6,0XDD,0XEE,0XDC,0XEE,0XDC,0XEE,0XDD,0XEE,0XBC,0XEE,0XBC,0XEE,
0XDC,0XEE,0XBC,0XEE,0X9C,0XEE,0X3B,0XE6,0X95,0XCC,0X70,0XA3,0X94,0XC4,0X94,0XC4,
0X53,0XC4,0XED,0X92,0XED,0X92,0X53,0XBC,0X33,0XBC,0X92,0X9B,0X9A,0XC5,0X7A,0XC5,
0X7B,0XCD,0X95,0XB3,0X34,0XB3,0XB9,0XCC,0X1B,0XDD,0X3A,0XDD,0X1A,0XD5,0X1A,0XD5,
0XFA,0XD4,0XFA,0XCC,0X5C,0XE6,0X5C,0XDE,0X3E,0XF7,0XBE,0XE6,0XFC,0XD5,0XF5,0XA3,
0X1B,0XDD,0X9C,0XE5,0XDC,0XE5,0XFD,0XE5,0XFD,0XE5,0X9C,0XED,0X55,0XAB,0XFC,0XCD,
0XDC,0XD5,0X51,0X9B,0X2E,0XA3,0XB4,0XCC,0XB5,0XCC,0XF5,0XCC,0X16,0XCD,0XF5,0XCC,
0X17,0XCD,0X7C,0XE6,0X7C,0XEE,0X9D,0XEE,0XBC,0XEE,0XDD,0XEE,0XDC,0XEE,0XDC,0XEE,
0XDD,0XEE,0XDC,0XEE,0XDC,0XEE,0XDC,0XEE,0XBC,0XEE,0XBC,0XEE,0X9C,0XEE,0X5C,0XEE,
0X34,0XBC,0X70,0XA3,0X12,0XB4,0X94,0XC4,0X33,0XBC,0XAC,0X8A,0XB1,0XAB,0XF2,0XB3,
0X74,0XC4,0X74,0XC4,0XD3,0XA3,0X5A,0XC5,0X33,0X93,0X96,0XBB,0XDA,0XD4,0X3A,0XD5,
0X3B,0XDD,0X3A,0XDD,0X1A,0XD5,0X1A,0XD5,0X3A,0XD5,0XB8,0XBC,0X7C,0XE6,0X7C,0XDE,
0X3E,0XF7,0X3E,0XF7,0X1E,0XEF,0X77,0XBC,0XB6,0XB3,0XDD,0XE5,0XFD,0XE5,0XFD,0XE5,
0XFD,0XE5,0X3B,0XDD,0X58,0XBC,0XFC,0XCD,0XDC,0XD5,0X71,0XA3,0X2E,0XA3,0X73,0XC4,
0X33,0XBC,0XD5,0XCC,0XF6,0XCC,0X15,0XCD,0XB5,0XC4,0X7C,0XE6,0X9C,0XEE,0X9C,0XEE,
0XBC,0XEE,0XDD,0XEE,0XDD,0XEE,0XDC,0XEE,0XBC,0XEE,0X7C,0XE6,0X78,0XCD,0X3B,0XE6,
0XBC,0XEE,0XDD,0XEE,0XBC,0XE6,0X3C,0XEE,0XB1,0XAB,0X50,0XA3,0XED,0X92,0X94,0XC4,
0XD1,0XAB,0XCD,0X8A,0X2F,0XA3,0X90,0XAB,0X54,0XC4,0X74,0XC4,0X74,0XBC,0X32,0X9B,
0XB6,0XB3,0X1B,0XDD,0X1A,0XDD,0X3A,0XD5,0X3A,0XDD,0X1A,0XDD,0X1A,0XD5,0X1A,0XD5,
0X1A,0XD5,0X7D,0XE6,0X9C,0XDE,0X7C,0XDE,0X3E,0XF7,0X3E,0XF7,0X1E,0XF7,0X38,0XB4,
0X7D,0XED,0XDD,0XE5,0XDD,0XE5,0XFD,0XE5,0XFC,0XE5,0X99,0XCC,0X3B,0XCD,0XFC,0XD5,
0X9B,0XD5,0X70,0XAB,0X4F,0XA3,0X94,0XC4,0X2E,0X9B,0X70,0XA3,0XD5,0XCC,0XF5,0XCC,
0XF6,0XCC,0X3B,0XE6,0X9C,0XE6,0X9C,0XEE,0XBD,0XEE,0XDD,0XEE,0XDC,0XEE,0XBC,0XE6,
0X7C,0XEE,0X6E,0X8A,0X6E,0X8A,0XAE,0X92,0X7C,0XEE,0XBD,0XEE,0X9C,0XEE,0X37,0XC5,
0XF6,0XD4,0XB0,0XAB,0X0E,0X9B,0X74,0XC4,0X4E,0XA3,0X0D,0X9B,0X2A,0X7A,0X4E,0XA3,
0X0E,0X9B,0X33,0XBC,0X34,0XBC,0X95,0XB3,0X18,0XC4,0X3A,0XDD,0X3B,0XDD,0X3B,0XDD,
0X3A,0XDD,0X1A,0XDD,0X1A,0XD5,0X1A,0XD5,0X7A,0XD5,0X9D,0XE6,0X9C,0XE6,0X5D,0XE6,
0X3E,0XF7,0X3E,0XF7,0X1E,0XEF,0XB5,0XAB,0XDC,0XE5,0XFC,0XE5,0XDD,0XE5,0XDD,0XE5,
0XFC,0XE5,0X18,0XC4,0X7B,0XD5,0XFB,0XCD,0X51,0X93,0X70,0XAB,0XCC,0X8A,0X94,0XC4,
0XD0,0XAB,0XF1,0XB3,0XB4,0XCC,0X15,0XCD,0X15,0XCD,0X54,0XB4,0X9C,0XE6,0XBC,0XEE,
0XDD,0XEE,0XDD,0XF6,0XDD,0XEE,0XDD,0XEE,0XD7,0XC4,0X52,0XB3,0XF9,0XDC,0XD9,0XDC,
0X14,0XB4,0XBB,0XE6,0XBC,0XEE,0X30,0X8B,0X16,0XD5,0XD1,0XB3,0X6E,0XA3,0X53,0XC4,
0X2E,0XA3,0X2E,0X9B,0X90,0XAB,0X2E,0XA3,0X2F,0XA3,0X2F,0X9B,0X32,0XA3,0X14,0XAB,
0XFB,0XDC,0X5B,0XDD,0X3B,0XDD,0X3A,0XD5,0X3A,0XDD,0X1A,0XD5,0X1B,0XD5,0X37,0XB4,
0X5D,0XE6,0X7D,0XE6,0X9C,0XE6,0X7C,0XE6,0X3E,0XF7,0X3E,0XF7,0X1E,0XEF,0X5A,0XD5,
0XDC,0XE5,0XDD,0XE5,0XFC,0XE5,0XFC,0XE5,0XDC,0XE5,0XF8,0XC3,0X95,0XA3,0X9B,0XCD,
0XB2,0XAB,0X90,0XAB,0XAC,0X8A,0X94,0XCC,0X90,0XAB,0X53,0XC4,0X12,0XBC,0XD5,0XC4,
0XD5,0XCC,0XB5,0XCC,0X9C,0XE6,0XBC,0XEE,0XDD,0XEE,0XDC,0XEE,0XBD,0XF6,0XDC,0XE6,
0X78,0XD5,0X19,0XDD,0X38,0XDD,0X39,0XDD,0X96,0XC4,0X9C,0XE6,0X7C,0XEE,0X58,0XD5,
0X0F,0X9B,0XB0,0XAB,0XD0,0XAB,0X90,0XAB,0X4E,0X9B,0X0D,0X9B,0X4F,0XA3,0X2E,0XA3,
0X0F,0XA3,0XAF,0X92,0X99,0XD4,0X5B,0XDD,0X7B,0XDD,0X5B,0XDD,0X3B,0XDD,0X1A,0XDD,
0X78,0XC4,0X1A,0XD5,0XFA,0XD4,0XF0,0X8A,0X99,0XD5,0X3C,0XDE,0X7C,0XDE,0X7C,0XDE,
0X3E,0XF7,0X3E,0XF7,0XFF,0XF6,0XDC,0XDD,0XFD,0XE5,0XDC,0XE5,0XFC,0XE5,0XFC,0XE5,
0XBC,0XE5,0XB8,0XC3,0XB7,0XBB,0X30,0X93,0X70,0XAB,0XEE,0XA2,0XED,0X9A,0XB5,0XCC,
0X2E,0X9B,0X11,0XBC,0XED,0X9A,0XB4,0XCC,0XF5,0XCC,0X90,0XA3,0XF9,0XDD,0XBC,0XEE,
0XDC,0XEE,0XDD,0XEE,0XBD,0XF6,0XBC,0XEE,0X9C,0XEE,0XF7,0XC4,0X9A,0XDD,0X3B,0XE6,
0X9C,0XEE,0X9D,0XEE,0XFA,0XDD,0X54,0XBC,0XCD,0X92,0X90,0XAB,0XD0,0XAB,0X6F,0XAB,
0X4F,0XA3,0X4F,0XA3,0X0F,0XA3,0XAF,0X9A,0X96,0XBB,0X1B,0XE5,0X3B,0XDD,0X5B,0XDD,
0X7B,0XDD,0X3B,0XDD,0X3A,0XD5,0X1A,0XD5,0X58,0XCC,0XFB,0XD4,0XEF,0X92,0XEE,0X92,
0XCE,0X92,0X4F,0X9B,0XF2,0XA3,0X1B,0XDE,0X3E,0XF7,0X3E,0XEF,0X1D,0XE6,0XFC,0XE5,
0XFC,0XE5,0XFD,0XE5,0XFD,0XE5,0XFD,0XE5,0XDC,0XE5,0XD7,0XC3,0X18,0XCC,0X71,0XAB,
0X70,0XB3,0X70,0XAB,0X2E,0X9B,0X94,0XC4,0X6F,0XA3,0X4F,0XA3,0XCD,0X9A,0X73,0XC4,
0XD4,0XCC,0XD5,0XCC,0X32,0XB4,0X3A,0XE6,0XBD,0XEE,0XDD,0XEE,0XDD,0XEE,0XDC,0XEE,
0XBD,0XEE,0XBD,0XEE,0XBC,0XEE,0XBC,0XEE,0XDD,0XE6,0XBC,0XE6,0X99,0XD5,0X90,0XAB,
0X0D,0X9B,0X8F,0XAB,0X90,0XAB,0X4F,0XA3,0X2F,0XA3,0XF0,0XA2,0XD1,0XA2,0XB6,0XBB,
0XFB,0XDC,0X5B,0XDD,0X5B,0XDD,0X5B,0XDD,0X5B,0XDD,0X3A,0XDD,0X1A,0XDD,0X54,0XAB,
0XBA,0XD4,0X53,0XA3,0XCD,0X8A,0XCC,0X8A,0XAC,0X8A,0XCC,0X8A,0XD1,0XB3,0XF2,0XAB,
0X3E,0XF7,0X3E,0XF7,0X19,0XC5,0XFC,0XE5,0XFC,0XDD,0XFD,0XE5,0XDD,0XE5,0XDD,0XE5,
0XDC,0XE5,0XBC,0XE5,0XBA,0XDC,0X72,0XAB,0XED,0X9A,0XB1,0XB3,0X4E,0X9B,0XB4,0XC4,
0XB1,0XAB,0XB1,0XB3,0X2E,0XA3,0X2E,0XA3,0XB4,0XC4,0XD4,0XCC,0X12,0XBC,0X70,0XA3,
0X98,0XDD,0X5B,0XE6,0X9C,0XEE,0XBC,0XEE,0XBC,0XEE,0XDD,0XEE,0XDD,0XEE,0X9C,0XEE,
0X9D,0XEE,0X99,0XD5,0X70,0XA3,0X70,0XAB,0XB0,0XB3,0X70,0XB3,0X90,0XAB,0X30,0XA3,
0XB5,0XBB,0XB8,0XC3,0X59,0XD4,0X5B,0XDD,0X5B,0XDD,0X5B,0XDD,0X5B,0XDD,0X5B,0XDD,
0X5B,0XDD,0X3A,0XD5,0X99,0XD4,0X53,0XAB,0XB5,0XB3,0XCE,0X92,0X4E,0XA3,0X2F,0XA3,
0X0E,0X9B,0XAC,0X8A,0X8B,0X82,0X4F,0X9B,0X5E,0XF7,0X1E,0XF7,0X76,0XB4,0XDC,0XE5,
0XFD,0XE5,0XFC,0XE5,0XFD,0XE5,0XFD,0XE5,0XDD,0XE5,0XDC,0XE5,0X3C,0XE5,0X12,0XA3,
0X71,0XAB,0XB1,0XB3,0X6F,0XA3,0XD5,0XCC,0X32,0XBC,0X90,0XAB,0X8C,0X8A,0X2E,0XA3,
0X73,0XC4,0XB4,0XCC,0XB4,0XC4,0XAC,0X8A,0XB1,0XB3,0X50,0XA3,0X12,0XB4,0X57,0XD5,
0XD9,0XE5,0X1B,0XE6,0X5C,0XEE,0X5C,0XEE,0XD5,0XBC,0X58,0XDD,0X71,0XAB,0X71,0XAB,
0XB0,0X9A,0XF5,0XBB,0X3A,0XDD,0X34,0XAB,0XF8,0XC3,0X3C,0XE5,0X7B,0XDD,0X7B,0XDD,
0X5B,0XDD,0X5B,0XDD,0X5B,0XDD,0X7B,0XDD,0X5B,0XDD,0X1B,0XDD,0X75,0XB3,0X54,0XB3,
0XCE,0X92,0XAD,0X92,0XAC,0X8A,0XF2,0XB3,0XF1,0XB3,0XF1,0XB3,0XD1,0XAB,0X6F,0XA3,
0X3E,0XF7,0X1E,0XF7,0XB7,0XB4,0XFD,0XE5,0XFD,0XE5,0XFD,0XE5,0XFD,0XE5,0XFD,0XE5,
0XDC,0XE5,0XDC,0XE5,0X99,0XCC,0X99,0XCC,0XD5,0XC3,0X32,0XAB,0XB1,0XAB,0XD5,0XCC,
0X53,0XBC,0X6F,0XAB,0X90,0XAB,0XED,0X9A,0X4F,0XA3,0X94,0XCC,0XB5,0XCC,0X74,0XC4,
0XCD,0X92,0X6F,0XAB,0X4F,0XAB,0X4F,0XA3,0X70,0XA3,0X33,0XBC,0X78,0XDD,0X78,0XDD,
0X78,0XDD,0X99,0XDD,0X74,0XAB,0X96,0XBB,0XB7,0XBB,0X5B,0XDD,0X9C,0XE5,0X5C,0XDD,
0X38,0XC4,0X7B,0XDD,0X7B,0XDD,0X9B,0XDD,0X5B,0XDD,0XB9,0XCC,0X7B,0XDD,0X5B,0XDD,
0X3B,0XD5,0XB7,0XBB,0X91,0X9A,0X6E,0X8A,0X2E,0X9B,0XAC,0X8A,0XAC,0X8A,0X8C,0X82,
0XD1,0XB3,0XF2,0XB3,0XF2,0XB3,0XF1,0XB3,0X3E,0XF7,0X3E,0XF7,0XF8,0XBC,0XDC,0XE5,
0XDC,0XE5,0XFD,0XE5,0XFD,0XE5,0XDC,0XE5,0XDD,0XE5,0XBC,0XE5,0XF6,0XBB,0XBC,0XE5,
0XF4,0XA2,0XF8,0XC3,0X34,0XBC,0XD5,0XCC,0X94,0XC4,0X30,0XA3,0X91,0XB3,0XAC,0X92,
0X2F,0XA3,0X4E,0XA3,0X2F,0XA3,0X2F,0XA3,0X2F,0XA3,0XEE,0X9A,0X2F,0XA3,0X2F,0XA3,
0XCE,0X92,0XB5,0XB3,0X5C,0XEE,0X3B,0XEE,0X7C,0XEE,0X9C,0XEE,0XDB,0XE5,0X96,0XBB,
0XB7,0XC3,0X7C,0XDD,0X9C,0XE5,0X9B,0XDD,0X3B,0XD5,0X5C,0XDD,0X7B,0XDD,0X9B,0XDD,
0X7B,0XE5,0X59,0XC4,0X3B,0XDD,0X5B,0XDD,0X79,0XCC,0X75,0XB3,0X2D,0X82,0XED,0X92,
0XB1,0XB3,0X2F,0X9B,0X6F,0XA3,0X8B,0X82,0X8C,0X8A,0XF1,0XB3,0XF1,0XB3,0XF1,0XB3,
0X3E,0XF7,0X3E,0XF7,0XDB,0XD5,0XFC,0XE5,0XFC,0XE5,0XDC,0XE5,0XFD,0XE5,0XFD,0XE5,
0XDC,0XE5,0XDD,0XED,0X1A,0XD5,0XDC,0XE5,0X9C,0XE5,0X1C,0XE5,0X95,0XC4,0XD5,0XCC,
0X74,0XCC,0X17,0XC4,0XD4,0XB3,0XF3,0XB3,0XD2,0XB3,0X30,0XA3,0XCE,0X9A,0X0F,0XA3,
0X50,0XA3,0XB2,0XB3,0X71,0XAB,0X8E,0X92,0X97,0XC3,0X76,0XB3,0X1C,0XEE,0X7D,0XEE,
0X9D,0XEE,0X9C,0XE6,0X9A,0XDD,0XD7,0XC3,0X3B,0XDD,0X9C,0XDD,0X9B,0XDD,0X9C,0XDD,
0X5C,0XDD,0X7B,0XDD,0X7B,0XDD,0X7B,0XDD,0X5B,0XDD,0XF7,0XBB,0X3B,0XDD,0XFB,0XDC,
0X96,0XB3,0X4E,0X8A,0XCD,0X92,0XCC,0X8A,0XF2,0XB3,0XD1,0XAB,0XB0,0XA3,0X4F,0XA3,
0X6F,0XA3,0XAC,0X8A,0XF1,0XB3,0XF1,0XB3,0X3E,0XF7,0X3E,0XF7,0XDE,0XF6,0XFC,0XDD,
0XDD,0XE5,0XFD,0XE5,0XFD,0XE5,0XDC,0XE5,0XDC,0XE5,0XDC,0XE5,0XDC,0XE5,0XDC,0XE5,
0XDC,0XE5,0X9C,0XED,0XB6,0XCC,0XD5,0XCC,0X54,0XC4,0X55,0XAB,0X7B,0XE5,0X7B,0XDD,
0X1A,0XDD,0X7B,0XE5,0XF9,0XD4,0XD1,0X92,0X5B,0XE5,0X7B,0XE5,0XD9,0XD4,0XF6,0XBB,
0X18,0XCC,0XF8,0XC3,0XD8,0XC3,0X18,0XC4,0XF7,0XBB,0X16,0XBC,0XD6,0XBB,0X18,0XCC,
0X7C,0XE5,0X9C,0XE5,0XBC,0XDD,0X9C,0XDD,0X5C,0XDD,0X5B,0XE5,0X7B,0XDD,0X7B,0XDD,
0X5B,0XDD,0X95,0XAB,0X3B,0XD5,0X96,0XBB,0X55,0XB3,0XAE,0X92,0XCC,0X92,0XAB,0X8A,
0X13,0XBC,0X12,0XB4,0X4F,0X9B,0X2F,0X9B,0XAC,0X8A,0XB1,0XAB,0XB0,0XAB,0XB0,0XAB,
0X3E,0XF7,0X3E,0XF7,0X1E,0XEF,0X1E,0XF7,0X7B,0XD5,0XFD,0XE5,0XDC,0XE5,0XDC,0XE5,
0XDC,0XE5,0XDC,0XE5,0XBC,0XE5,0XFC,0XE5,0XDC,0XE5,0X5A,0XE5,0XD5,0XCC,0XD5,0XCC,
0X54,0XBC,0X7B,0XDD,0X95,0XAB,0XBC,0XE5,0X9C,0XE5,0X7B,0XDD,0XBC,0XE5,0XBC,0XE5,
0XFA,0XD4,0X9C,0XDD,0XBC,0XE5,0XBC,0XE5,0XBC,0XE5,0XBC,0XE5,0X7C,0XE5,0X3B,0XDD,
0X3B,0XDD,0XB6,0XB3,0X3B,0XDD,0X7C,0XDD,0X94,0XAB,0X7C,0XE5,0X9C,0XDD,0X9C,0XE5,
0X37,0XBC,0X7B,0XDD,0X7B,0XDD,0X3B,0XD5,0X3B,0XDD,0XD3,0XA2,0XB9,0XD4,0X77,0XBB,
0XD1,0X9A,0XAD,0X92,0XAC,0X8A,0XED,0X92,0X12,0XB4,0X12,0XBC,0X4F,0X9B,0XEF,0X92,
0X0E,0X93,0XAC,0X8A,0X6F,0XA3,0XCC,0X8A,0X3E,0XF7,0X3F,0XF7,0X3E,0XF7,0X1E,0XF7,
0XFE,0XF6,0XF8,0XC4,0XDC,0XE5,0XDC,0XE5,0XDC,0XE5,0XDC,0XE5,0XDC,0XE5,0XDC,0XE5,
0XBC,0XE5,0XD4,0XB3,0XD5,0XCC,0XD5,0XCC,0X92,0XAB,0X9C,0XE5,0XBC,0XE5,0XBC,0XE5,
0XBC,0XE5,0XDD,0XE5,0XBC,0XE5,0XBC,0XE5,0XBC,0XE5,0X16,0XBC,0X9C,0XE5,0XBB,0XE5,
0XBB,0XE5,0XDC,0XE5,0XBC,0XE5,0X9C,0XE5,0X7C,0XE5,0X3B,0XDD,0X77,0XBC,0XFC,0XE5,
0X5B,0XDD,0XFB,0XDC,0XB9,0XC4,0X5C,0XDD,0X1A,0XD5,0X7B,0XDD,0X7B,0XDD,0X3B,0XDD,
0XFB,0XDC,0XB3,0X9A,0X76,0XB3,0X75,0XBB,0X10,0XA3,0X4B,0X82,0XCC,0X8A,0XB0,0XAB,
0X12,0XB4,0XF2,0XB3,0X12,0XB4,0XD6,0XC4,0XEE,0X8A,0XCD,0X8A,0X8B,0X82,0XCC,0X8A,
0X3E,0XF7,0X3E,0XF7,0X3E,0XF7,0X3E,0XF7,0X3E,0XF7,0X1E,0XEF,0XF8,0XBC,0XBD,0XE5,
0XDC,0XE5,0XBC,0XE5,0XBC,0XE5,0XDC,0XE5,0XBC,0XE5,0X54,0XC4,0XB5,0XCC,0XB4,0XC4,
0X94,0XB3,0X9C,0XE5,0XDC,0XE5,0XDC,0XE5,0XDC,0XE5,0XDC,0XE5,0XDC,0XE5,0X77,0XBC,
0XBB,0XDD,0X9B,0XE5,0XB9,0XD4,0XBB,0XE5,0XDC,0XE5,0XDC,0XE5,0XBB,0XE5,0X9C,0XE5,
0X3B,0XDD,0X9B,0XDD,0X7B,0XDD,0X3A,0XD5,0X5B,0XDD,0X1A,0XDD,0X5B,0XDD,0X5B,0XDD,
0X7B,0XDD,0X7B,0XDD,0X7B,0XDD,0X5B,0XDD,0X59,0XCC,0XB2,0XA2,0X56,0XBB,0X33,0XAB,
0XED,0X92,0XED,0X92,0XCC,0X92,0X8F,0XA3,0X12,0XBC,0X12,0XBC,0X12,0XB4,0X35,0XAC,
0XAE,0X82,0XCD,0X8A,0XAC,0X8A,0X6B,0X7A,0X3E,0XF7,0X3E,0XEF,0X1E,0XEF,0X1E,0XF7,
0X3E,0XF7,0X3E,0XF7,0X1E,0XEF,0XB3,0X9B,0X9B,0XE5,0XDC,0XE5,0XDC,0XE5,0XBC,0XE5,
0X9C,0XE5,0XB5,0XCC,0XB5,0XCC,0X95,0XCC,0X37,0XC4,0XDC,0XE5,0XBC,0XE5,0XDC,0XE5,
0XDC,0XE5,0XDC,0XE5,0XDC,0XE5,0X3B,0XDD,0X9C,0XE5,0XBC,0XE5,0X5B,0XE5,0X7C,0XE5,
0X9C,0XE5,0XBB,0XE5,0XBB,0XDD,0X9C,0XE5,0XF9,0XCC,0X9B,0XE5,0X9C,0XE5,0X9C,0XDD,
0X9B,0XDD,0X37,0XBC,0X7B,0XDD,0X7B,0XDD,0X7B,0XDD,0X7B,0XDD,0X7B,0XD5,0X5B,0XDD,
0X79,0XCC,0XF3,0XA2,0X55,0XB3,0X6D,0X8A,0XED,0X92,0XCD,0X92,0XAC,0X8A,0XCC,0X8A,
0XF2,0XB3,0X12,0XB4,0X12,0XB4,0X50,0X9B,0XD8,0XC4,0XEE,0X8A,0XAD,0X8A,0XCD,0X8A,
0X3E,0XF7,0X3E,0XF7,0X3E,0XEF,0X3E,0XEF,0X3E,0XF7,0X3E,0XF7,0X1E,0XF7,0XF3,0XA3,
0XB3,0XB3,0XB4,0XAB,0XFA,0XD4,0X9C,0XE5,0X93,0XAB,0XB5,0XCC,0XB5,0XCC,0X72,0XAB,
0XDA,0XD4,0XBC,0XED,0XDC,0XE5,0XDC,0XE5,0XDC,0XE5,0XDC,0XE5,0XBC,0XE5,0X7C,0XE5,
0X5C,0XDD,0XBB,0XE5,0X9B,0XDD,0X7B,0XDD,0XB9,0XCC,0X7B,0XE5,0X7C,0XDD,0XF6,0XB3,
0X9C,0XE5,0X9C,0XE5,0X9C,0XDD,0X9B,0XDD,0X9C,0XDD,0X5C,0XDD,0X5C,0XDD,0X7B,0XDD,
0X7B,0XDD,0X5B,0XDD,0X5B,0XDD,0X5B,0XDD,0X1B,0XDD,0X34,0XB3,0X74,0XB3,0XAE,0X92,
0XCD,0X92,0XAC,0X8A,0X6D,0X7A,0XCD,0X92,0XF2,0XB3,0X12,0XB4,0X32,0XB4,0XD2,0XAB,
0XD8,0XC4,0X72,0X93,0XCE,0X8A,0XCD,0X8A,0X3E,0XF7,0X1E,0XEF,0X3E,0XF7,0X3E,0XEF,
0X3E,0XF7,0X3E,0XEF,0X1E,0XEF,0XB1,0X9B,0X91,0XAB,0X7D,0XF6,0X73,0XAB,0XF6,0XC3,
0X75,0XCC,0XB5,0XCC,0XB4,0XC4,0XB5,0XBB,0X5B,0XE5,0XBC,0XE5,0XDC,0XE5,0XDC,0XE5,
0XDC,0XE5,0XDC,0XE5,0XBC,0XE5,0XBB,0XDD,0XB6,0XB3,0XBB,0XE5,0XBB,0XE5,0XBB,0XE5,
0X9C,0XDD,0X7C,0XDD,0X5B,0XDD,0X7B,0XDD,0X9C,0XE5,0X7B,0XE5,0X9C,0XDD,0X9B,0XDD,
0X9C,0XDD,0X7B,0XDD,0XF6,0XB3,0X7B,0XE5,0X7B,0XDD,0X5B,0XDD,0X7B,0XDD,0X5B,0XDD,
0X1B,0XDD,0XB2,0X9A,0X6E,0X8A,0XAC,0X8A,0XAD,0X8A,0XAC,0X8A,0X13,0XAC,0X0E,0X93,
0X6F,0XA3,0XF1,0XB3,0X12,0XB4,0XF1,0XB3,0X91,0XA3,0XD8,0XC4,0XAF,0X82,0X34,0XAC,
0X1E,0XEF,0X1E,0XF7,0XDD,0XEE,0XFE,0XEE,0X1E,0XF7,0X1E,0XEF,0X1D,0XEF,0XB2,0X9B,
0X92,0XB3,0X7D,0XEE,0X14,0XB4,0X2F,0X9B,0XB4,0XC4,0XB5,0XCC,0X34,0XC4,0XB6,0XC3,
0X79,0XCC,0X9C,0XE5,0XBC,0XE5,0XBC,0XE5,0XBC,0XE5,0XBC,0XE5,0XBC,0XE5,0XBB,0XE5,
0X7C,0XDD,0X9C,0XE5,0XBB,0XE5,0XBB,0XE5,0XBC,0XE5,0X9C,0XDD,0XBC,0XDD,0X9C,0XDD,
0X9C,0XDD,0X9C,0XDD,0X7B,0XDD,0X9B,0XDD,0X9C,0XDD,0X7B,0XDD,0X3B,0XD5,0X5B,0XDD,
0X7B,0XDD,0X5B,0XDD,0X5B,0XDD,0X3B,0XDD,0X3A,0XDD,0X37,0XC4,0XAE,0X92,0XAC,0X8A,
0XCC,0X8A,0XAD,0X8A,0XD8,0XCC,0X4F,0X93,0X2E,0X9B,0XF2,0XB3,0XF2,0XB3,0XD2,0XB3,
0XD2,0XB3,0X30,0X93,0XF9,0XC4,0XD9,0XC4,0X98,0XC5,0X5C,0XE6,0X5C,0XE6,0XFD,0XEE,
0X1E,0XEF,0X3E,0XEF,0X1E,0XEF,0X54,0XAC,0X92,0XAB,0X7C,0XEE,0XD2,0XAB,0X93,0XC4,
0XB4,0XCC,0X94,0XC4,0X8F,0X92,0X96,0XC3,0XB7,0XC3,0XF6,0XBB,0X9C,0XE5,0XDC,0XE5,
0XBC,0XED,0XBC,0XE5,0XBB,0XE5,0XBC,0XE5,0X9C,0XE5,0X3B,0XDD,0X9B,0XDD,0XBB,0XDD,
0X9C,0XE5,0X9C,0XDD,0X9B,0XDD,0X9C,0XDD,0X9C,0XDD,0X9C,0XDD,0X9C,0XDD,0X9C,0XDD,
0XD9,0XC4,0X3D,0XE6,0X7B,0XDD,0X5B,0XDD,0X7B,0XDD,0X3B,0XD5,0X3A,0XD5,0X3A,0XDD,
0X3A,0XDD,0XFA,0XD4,0XEF,0X92,0XCD,0X92,0XED,0X92,0XCD,0X92,0X18,0XCD,0XEF,0X82,
0XAD,0X82,0X6F,0XA3,0XD2,0XB3,0XF1,0XB3,0XF1,0XB3,0XD1,0XAB,0X14,0XAC,0XF9,0XC4,
0X7C,0XEE,0XF7,0XBC,0X38,0XC5,0X1E,0XEF,0X1E,0XF7,0X1E,0XEF,0XFE,0XEE,0X1B,0XDE,
0X91,0XAB,0XB5,0XBC,0XD6,0XC4,0XB4,0XCC,0XB4,0XCC,0X74,0XC4,0X4F,0XA3,0X10,0XAB,
0XF1,0XA2,0XB6,0XBB,0X75,0XB3,0X7B,0XE5,0XBB,0XE5,0XDC,0XED,0XBB,0XE5,0XBC,0XE5,
0X9C,0XE5,0XB5,0XAB,0X9C,0XE5,0XBC,0XDD,0X9C,0XDD,0X9C,0XDD,0X9C,0XDD,0X9C,0XDD,
0X9C,0XDD,0X9B,0XDD,0X7B,0XDD,0X1A,0XCD,0X1A,0XCD,0XB9,0XBC,0X1C,0XE6,0X3C,0XE6,
0X3D,0XEE,0X3C,0XE6,0X3D,0XE6,0X9B,0XD5,0XDC,0XE5,0XF9,0XCC,0X14,0XAC,0X38,0XD5,
0X30,0X93,0XF0,0X92,0X19,0XCD,0XB8,0XBC,0X56,0XB4,0X0F,0X93,0XD2,0XB3,0XF1,0XB3,
0XD1,0XB3,0XD2,0XB3,0XD2,0XAB,0XD4,0X9B,0X99,0XD5,0X95,0XBC,0X3C,0XE6,0XDD,0XEE,
0X1E,0XF7,0X1E,0XEF,0X1E,0XEF,0X9D,0XEE,0XB1,0XAB,0X70,0X9B,0X95,0XC4,0XB4,0XC4,
0XB1,0XAB,0XCD,0X92,0X2E,0X9B,0X0E,0X9B,0XED,0X9A,0XEF,0XA2,0XD1,0X9A,0X34,0XA3,
0X3B,0XDD,0X9C,0XE5,0X9C,0XE5,0XBC,0XE5,0X9B,0XDD,0X1A,0XD5,0X9C,0XE5,0X9C,0XDD,
0X9C,0XDD,0X9C,0XE5,0X77,0XBC,0XF9,0XCC,0XB8,0XBC,0XF9,0XC4,0X5D,0XEE,0X5C,0XE6,
0X3C,0XE6,0X3D,0XE6,0X3D,0XE6,0XBC,0XE5,0X3A,0XCD,0X3A,0XD5,0XFC,0XE5,0X1C,0XE6,
0X3C,0XE6,0X1C,0XDE,0X3B,0XDE,0X1C,0XDE,0XFB,0XDD,0X7B,0XD5,0X18,0XB4,0X95,0XA3,
0X32,0X8B,0XB3,0XA3,0XEF,0X92,0X71,0XA3,0XD2,0XAB,0XD2,0XAB,0XB7,0XBC,0X5A,0XCD,
0XDA,0XDD,0X79,0XDD,0X1B,0XE6,0XBA,0XD5,0X1E,0XEF,0X1D,0XEF,0X1E,0XEF,0XDD,0XEE,
0XB2,0XAB,0X0E,0X9B,0X94,0XC4,0X73,0XBC,0X53,0XBC,0X0E,0X9B,0XED,0X9A,0XCD,0X92,
0XCD,0X9A,0XED,0X9A,0XED,0X92,0X30,0XA3,0X11,0XA3,0XFB,0XDC,0X9B,0XDD,0X9B,0XE5,
0X9C,0XDD,0X5B,0XE5,0X5C,0XDD,0X9C,0XE5,0X3A,0XD5,0X1D,0XE6,0X7D,0XE6,0X7D,0XE6,
0X5C,0XE6,0X5C,0XEE,0X7C,0XE6,0X5D,0XE6,0X5A,0XD5,0X78,0XBC,0X36,0XAC,0X37,0XB4,
0X19,0XCD,0XFD,0XE5,0XFD,0XE5,0X1C,0XDE,0X1C,0XDE,0XDB,0XDD,0X59,0XCD,0X36,0XB4,
0XF6,0XAB,0X37,0XBC,0X18,0XBC,0X18,0XB4,0X1A,0XCD,0XBB,0XDD,0XDB,0XDD,0XBA,0XDD,
0X9A,0XD5,0X7A,0XD5,0X15,0XA4,0XDB,0XD5,0X98,0XDD,0X58,0XD5,0X55,0XB4,0X96,0XB4,
0XDE,0XEE,0X1E,0XEF,0XFE,0XEE,0X3C,0XDE,0XB2,0XA3,0X94,0XC4,0X94,0XC4,0XCD,0X8A,
0X6F,0XA3,0X0E,0X9B,0XCD,0X9A,0X8C,0X92,0XCD,0X92,0XED,0X92,0XCD,0X92,0X0E,0XA3,
0X0E,0X9B,0XD0,0X9A,0X99,0XCC,0X7C,0XE5,0X9C,0XE5,0X9B,0XDD,0XBA,0XD4,0XD9,0XC4,
0X3D,0XE6,0X7C,0XE6,0X7D,0XEE,0X7D,0XE6,0X5C,0XE6,0X3C,0XE6,0XF7,0XBC,0X1C,0XE6,
0X3D,0XE6,0X3C,0XE6,0X5C,0XE6,0X5C,0XE6,0X3C,0XE6,0X3C,0XE6,0X3D,0XE6,0X5C,0XE6,
0X3C,0XE6,0X3C,0XE6,0X1C,0XDE,0X1C,0XDE,0X1B,0XDE,0XFC,0XDD,0X9B,0XDD,0XB5,0XA3,
0X18,0XBC,0X58,0XBC,0XFA,0XCC,0X37,0XB4,0X58,0XBC,0XDA,0XCC,0X5A,0XD5,0X3A,0XCD,
0X78,0XD5,0X78,0XD5,0X54,0XB4,0XDA,0XDD,0X58,0XC5,0X38,0XB5,0XDB,0XCD,0XBB,0XCD,
0X71,0X93,0X95,0XC4,0X53,0XBC,0X12,0XBC,0XEE,0X92,0X0E,0XA3,0X2E,0XA3,0XAC,0X92,
0XCC,0X92,0X6F,0XA3,0XED,0X92,0XED,0X9A,0XED,0X9A,0XEE,0X9A,0XB0,0X92,0X18,0XC4,
0X5C,0XDD,0X9B,0XDD,0X37,0XBC,0X1D,0XE6,0X7C,0XE6,0X7D,0XEE,0X3C,0XE6,0XB6,0XB4,
0X5D,0XE6,0X7D,0XEE,0X7D,0XE6,0X5D,0XE6,0X5D,0XE6,0X5D,0XE6,0X5C,0XE6,0X5C,0XE6,
0X5C,0XE6,0X5C,0XE6,0X3C,0XE6,0X3C,0XE6,0X3C,0XE6,0X3C,0XDE,0X1C,0XDE,0X1C,0XE6,
0X1C,0XE6,0X1B,0XDE,0XFB,0XDD,0XFB,0XDD,0X9B,0XDD,0X1A,0XCD,0X75,0XA3,0X99,0XC4,
0X99,0XC4,0X99,0XBC,0X98,0XC4,0XD9,0XC4,0X99,0XDD,0X58,0XDD,0X95,0XBC,0XD3,0X9B,
0XF7,0XB4,0XBB,0XCD,0XBB,0XCD,0X7A,0XCD,0X74,0XBC,0X94,0XC4,0XB1,0XAB,0X54,0XBC,
0XD7,0XCC,0X2F,0XA3,0X12,0XBC,0XCD,0X92,0XAC,0X8A,0X13,0XBC,0XCD,0X92,0XED,0X92,
0XCD,0X92,0XED,0X92,0X0E,0X9B,0X8F,0X92,0XF7,0XBB,0X3B,0XDD,0X1D,0XEE,0X5C,0XE6,
0X7D,0XE6,0X38,0XC5,0X5D,0XEE,0X5C,0XE6,0X7D,0XE6,0X7D,0XE6,0X5C,0XE6,0X7C,0XE6,
0X5C,0XE6,0X5D,0XE6,0X5C,0XE6,0X5C,0XE6,0X3C,0XEE,0X5C,0XE6,0X3C,0XE6,0X3C,0XE6,
0X3C,0XE6,0X1C,0XDE,0X1C,0XDE,0X1C,0XE6,0X1C,0XDE,0X1B,0XDE,0X1B,0XDE,0XDB,0XDD,
0XF9,0XC4,0X37,0XB4,0XF7,0XB3,0XD7,0XB3,0XDA,0XCC,0X7A,0XDD,0X9A,0XD5,0X9A,0XD5,
0XDA,0XDD,0X37,0XD5,0XB6,0XBC,0XBB,0XCD,0X9B,0XCD,0XBB,0XCD,0X9B,0XC5,0XB3,0X9B,
0X94,0XC4,0X54,0XBC,0X70,0XA3,0XEE,0X92,0X7A,0XD5,0X70,0XA3,0X53,0XC4,0X0E,0X9B,
0XF2,0XBB,0X53,0XBC,0X4E,0X9B,0XF1,0XB3,0XED,0X92,0XED,0X92,0X0E,0X9B,0XEE,0X8A,
0X11,0X9B,0X37,0XBC,0X5C,0XEE,0X3D,0XE6,0XD8,0XC4,0X5C,0XE6,0X7C,0XE6,0X7C,0XE6,
0X5D,0XE6,0X5D,0XE6,0X5D,0XE6,0X5D,0XE6,0X3C,0XE6,0X5C,0XE6,0X3C,0XE6,0X3C,0XE6,
0X3C,0XE6,0X3C,0XE6,0X5C,0XE6,0X3C,0XE6,0X3C,0XDE,0X1C,0XDE,0X1C,0XE6,0X1C,0XE6,
0X1C,0XDE,0XFB,0XDD,0X1B,0XDE,0XFB,0XDD,0XDB,0XDD,0XBB,0XD5,0X9B,0XD5,0XB9,0XC4,
0X17,0XB4,0X37,0XB4,0X7A,0XD5,0X9A,0XD5,0X58,0XCD,0X58,0XD5,0XF4,0XA3,0XBB,0XCD,
0X7B,0XC5,0XBB,0XCD,0XBA,0XC5,0X75,0XBC,0X94,0XC4,0X30,0X93,0X30,0X93,0XB1,0XAB,
0X30,0X93,0X10,0X93,0X53,0XC4,0X90,0XAB,0X74,0XC4,0X4F,0XA3,0XB0,0XAB,0X33,0XBC,
0X0E,0X9B,0X0E,0X9B,0X2F,0X9B,0X76,0XBC,0X78,0XC4,0X3C,0XEE,0XDB,0XDD,0X5D,0XE6,
0X5D,0XEE,0X7D,0XE6,0X5D,0XE6,0X7D,0XE6,0X7D,0XE6,0X5C,0XE6,0X5D,0XE6,0X5C,0XE6,
0X5C,0XE6,0X5C,0XE6,0X1C,0XE6,0XFC,0XE5,0X3D,0XE6,0X3C,0XDE,0X3C,0XDE,0X3C,0XDE,
0X1C,0XE6,0X1C,0XE6,0X1C,0XE6,0X1B,0XDE,0X1B,0XDE,0X1B,0XDE,0X1B,0XDE,0XDA,0XDD,
0XBB,0XDD,0XBB,0XDD,0XBA,0XD5,0X9A,0XD5,0X9A,0XD5,0X7A,0XD5,0X7A,0XD5,0X7A,0XD5,
0XD4,0X93,0X58,0XD5,0XB8,0XBC,0XBB,0XCD,0X9B,0XCD,0X97,0XAC,0X9A,0XCD,0X54,0XC4,
0X94,0XC4,0XDB,0XDD,0XF4,0XAB,0X71,0X9B,0XB2,0XAB,0XD7,0XC4,0X32,0XB4,0X54,0XC4,
0X73,0XC4,0X96,0XC4,0X90,0XA3,0X53,0XBC,0X14,0XB4,0X34,0XB4,0X3C,0XE6,0X99,0XC4,
0XBD,0XE5,0X3A,0XCD,0X3D,0XEE,0X7D,0XE6,0X7D,0XE6,0X5C,0XE6,0X5C,0XE6,0X5D,0XE6,
0X5D,0XE6,0X5C,0XEE,0X3C,0XE6,0X5C,0XE6,0XFD,0XE5,0XB9,0XC4,0X79,0XC4,0XF9,0XC4,
0X1C,0XE6,0X3C,0XE6,0X1C,0XDE,0X1C,0XE6,0X3C,0XDE,0X1C,0XE6,0X1B,0XDE,0X1B,0XDE,
0X1B,0XDE,0XFB,0XDD,0XDB,0XDD,0XDB,0XDD,0XDA,0XDD,0XBB,0XDD,0XBA,0XD5,0X9A,0XD5,
0XBA,0XD5,0X9A,0XD5,0X7A,0XD5,0X79,0XD5,0XD9,0XA4,0XD4,0X93,0X7A,0XCD,0XB8,0XAC,
0XFC,0XDD,0X3C,0XDE,0X59,0XD5,0X54,0XC4,0X94,0XC4,0X9D,0XE6,0X35,0XAC,0X7D,0XE6,
0X3C,0XE6,0X78,0XCD,0X33,0XBC,0X54,0XC4,0X54,0XC4,0X18,0XD5,0X30,0X9B,0X3C,0XE6,
0X5D,0XEE,0X3C,0XE6,0XF9,0XC4,0X7A,0XC4,0X39,0XC4,0X3B,0XD5,0X5C,0XE6,0X5D,0XE6,
0X5D,0XE6,0X7D,0XE6,0X5D,0XE6,0X5C,0XE6,0X3C,0XE6,0X3C,0XE6,0XFD,0XDD,0XF7,0XAB,
0X59,0XBC,0X1A,0XCD,0X1C,0XE6,0X1C,0XDE,0X3C,0XE6,0X3C,0XDE,0X1C,0XDE,0X1C,0XE6,
0X1C,0XDE,0X1C,0XDE,0X1B,0XDE,0X1B,0XDE,0X1B,0XDE,0XFB,0XDD,0XDB,0XDD,0XDB,0XDD,
0XDB,0XDD,0XBB,0XD5,0XBA,0XD5,0X9A,0XD5,0X9A,0XD5,0X9A,0XD5,0X7A,0XD5,0X79,0XD5,
0XFA,0X9C,0X54,0X83,0XDC,0XD5,0X9D,0XE6,0XDD,0XEE,0XBD,0XE6,0X95,0XB4,0X74,0XC4,
0XF3,0XAB,0XBD,0XE6,0X76,0XB4,0XBD,0XE6,0XDD,0XE6,0XBD,0XEE,0X55,0XB4,0X39,0XCD,
0X3D,0XEE,0X1C,0XE6,0X3C,0XE6,0X5C,0XE6,0X3C,0XE6,0X5B,0XD5,0X7A,0XC4,0X35,0XA3,
0X38,0XBC,0X3D,0XE6,0X5D,0XE6,0X5C,0XE6,0X5C,0XE6,0X5D,0XE6,0X5C,0XE6,0X3C,0XE6,
0XFC,0XE5,0XF7,0XAB,0X78,0XBC,0X1A,0XCD,0X1D,0XE6,0X1C,0XE6,0X1C,0XE6,0X3C,0XDE,
0X3C,0XDE,0X1C,0XE6,0X1C,0XDE,0X1C,0XE6,0X1C,0XE6,0X1B,0XDE,0X1C,0XDE,0XFC,0XDD,
0XFB,0XDD,0XDB,0XDD,0XBB,0XDD,0XBB,0XDD,0XBB,0XDD,0XBB,0XD5,0X7A,0XD5,0X7A,0XD5,
0X7A,0XD5,0X5A,0XCD,0X3A,0XCD,0X39,0XCD,0XD9,0X9C,0X5A,0XC5,0XBD,0XEE,0XDD,0XE6,
0XDD,0XEE,0XBD,0XE6,0X14,0XAC,0X74,0XC4,0X13,0XA4,0XBD,0XE6,0X14,0XA4,0XBD,0XE6,
0XBD,0XEE,0X96,0XAC,0X5C,0XE6,0X7D,0XE6,0X7D,0XE6,0X5C,0XE6,0X5D,0XE6,0X5D,0XEE,
0XBB,0XDD,0X55,0X9B,0X59,0XBC,0X7A,0XC4,0X9A,0XC4,0X1D,0XE6,0X3C,0XE6,0X5D,0XE6,
0X5D,0XE6,0X5C,0XE6,0X3C,0XE6,0XD9,0XC4,0X39,0XBC,0X98,0XC4,0X1C,0XE6,0X3C,0XE6,
0X3C,0XE6,0X3C,0XDE,0X3C,0XDE,0X1C,0XDE,0X1C,0XDE,0X1C,0XE6,0X1C,0XE6,0X1B,0XDE,
0X1B,0XDE,0X1B,0XDE,0XFB,0XDD,0XFB,0XDD,0XDB,0XDD,0XBB,0XDD,0XBB,0XDD,0XBA,0XD5,
0X9A,0XDD,0XB8,0XC4,0X38,0XB4,0XF7,0XB3,0XF7,0XB3,0XD7,0XB3,0XF7,0XB3,0XB6,0XAB,
0XBA,0XA4,0X7D,0XE6,0XDD,0XEE,0XDD,0XE6,0XDD,0XEE,0XBD,0XE6,0X33,0XAC,0X34,0XB4,
0X5B,0XE6,0XBD,0XE6,0X79,0XCD,0X9D,0XE6,0X1C,0XDE,0X7D,0XE6,0X7D,0XE6,0X7D,0XE6,
0X7D,0XEE,0X5C,0XE6,0X1C,0XE6,0X1C,0XE6,0XBD,0XE5,0X7A,0XC4,0X59,0XC4,0X59,0XC4,
0X7A,0XC4,0XDC,0XE5,0X3C,0XE6,0X5C,0XE6,0X5C,0XE6,0X1C,0XDE,0XF6,0XAB,0X59,0XC4,
0X1B,0XD5,0X3C,0XE6,0X3C,0XDE,0X3C,0XE6,0X1C,0XDE,0X3C,0XDE,0X3C,0XDE,0X1C,0XE6,
0X1C,0XDE,0X1C,0XDE,0X1B,0XDE,0X1B,0XDE,0X1B,0XDE,0X1B,0XDE,0XFB,0XDD,0XDB,0XDD,
0XDB,0XDD,0XBA,0XDD,0XBA,0XDD,0XDA,0XD5,0XBA,0XD5,0X9A,0XD5,0X7A,0XD5,0X5A,0XD5,
0X3A,0XCD,0XD9,0XCC,0X57,0XBC,0XD6,0XAB,0X57,0X9C,0XBD,0XE6,0XDD,0XE6,0XDD,0XE6,
0XBD,0XE6,0XBD,0XE6,0X7D,0XE6,0X9D,0XEE,0X9D,0XE6,0XBC,0XE6,0X5C,0XDE,0XDB,0XDD,
0X7D,0XEE,0X7C,0XE6,0X7D,0XE6,0X7D,0XE6,0X5D,0XE6,0X77,0XB4,0X3C,0XE6,0X7B,0XD5,
0X7A,0XC4,0X7A,0XC4,0X59,0XC4,0X59,0XC4,0X59,0XC4,0X7A,0XC4,0XDA,0XCC,0X1A,0XCD,
0XFA,0XCC,0X79,0XC4,0X79,0XBC,0X1A,0XCD,0X3C,0XE6,0X3D,0XE6,0X3C,0XDE,0X3C,0XDE,
0X1C,0XDE,0X3C,0XE6,0X1C,0XE6,0X1C,0XE6,0X1C,0XDE,0X1B,0XDE,0XFC,0XDD,0XFC,0XDD,
0XFC,0XDD,0XFB,0XDD,0XDB,0XDD,0XDB,0XDD,0XBB,0XDD,0XBB,0XDD,0XBA,0XD5,0X9A,0XD5,
0X9A,0XD5,0X9A,0XD5,0X9A,0XCD,0X7A,0XD5,0X5A,0XD5,0X5A,0XD5,0X3A,0XCD,0X19,0XCD,
0X5D,0XE6,0XBD,0XE6,0XBD,0XE6,0XBD,0XE6,0X7C,0XE6,0X58,0XC5,0XDA,0XD5,0XFB,0XDD,
0X5D,0XE6,0X39,0XC5,0X5C,0XE6,0X5D,0XE6,0X5D,0XE6,0X5D,0XE6,0X3C,0XE6,0X19,0XC5,
0X1C,0XDE,0X3C,0XE6,0X3A,0XCD,0XD9,0XC4,0X99,0XC4,0X79,0XC4,0X79,0XC4,0X79,0XC4,
0X59,0XBC,0X5A,0XBC,0X39,0XBC,0X59,0XC4,0X59,0XBC,0X3A,0XC4,0X79,0XBC,0X1C,0XE6,
0X3C,0XDE,0X3C,0XE6,0X1C,0XDE,0X1C,0XE6,0X1C,0XE6,0X1C,0XE6,0X1B,0XDE,0X1B,0XDE,
0X1B,0XDE,0X1B,0XDE,0XFB,0XDD,0XFB,0XDD,0XFB,0XDD,0XDB,0XDD,0XBB,0XDD,0XBB,0XDD,
0XDB,0XDD,0X7A,0XD5,0XB4,0XA3,0X57,0XBC,0X19,0XD5,0X3A,0XD5,0X7B,0XD5,0X5A,0XD5,
0X5A,0XD5,0X3A,0XD5,0X3A,0XCD,0X19,0XCD,0X9D,0XE6,0X7C,0XE6,0X99,0XCD,0X18,0XBD,
0X3C,0XE6,0X5C,0XE6,0X5D,0XE6,0X5D,0XE6,0X5C,0XE6,0X5C,0XE6,0X3C,0XE6,0X3C,0XE6,
0X3C,0XE6,0XFB,0XE5,0XFC,0XDD,0X3C,0XE6,0X5C,0XE6,0X5C,0XE6,0X5C,0XE6,0X3C,0XE6,
0X3C,0XE6,0X3C,0XE6,0X1C,0XE6,0X1D,0XE6,0XDC,0XDD,0XD9,0XC4,0X39,0XBC,0X39,0XBC,
0X39,0XC4,0X39,0XBC,0XDC,0XDD,0X3C,0XDE,0X1C,0XDE,0X1C,0XDE,0X1C,0XDE,0X1C,0XE6,
0X1C,0XE6,0X1C,0XDE,0X1B,0XDE,0X1B,0XDE,0X1B,0XDE,0X1B,0XDE,0XDB,0XDD,0XDB,0XDD,
0XDB,0XDD,0XBA,0XDD,0XBB,0XDD,0XDB,0XD5,0XDB,0XD5,0XBA,0XD5,0X7A,0XD5,0X5B,0XD5,
0XD6,0XAB,0X95,0XAB,0X95,0XA3,0X17,0XB4,0XF9,0XCC,0X19,0XCD,0X19,0XCD,0X19,0XCD,
};


#endif
